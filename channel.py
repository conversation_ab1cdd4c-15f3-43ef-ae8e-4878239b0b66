import numpy as np


def generate_channel(
    U,
    C,
    area_radius_km=1.0,
    cell_radius_km=0.5,
    PL_model=None,
    shadowing_std=8,
    seed=None,
):
    """
    生成单个时刻的信道增益矩阵

    Returns:
        H_power: (U, C) 信道功率增益矩阵
    """

    if seed is not None:
        np.random.seed(seed)

    if PL_model is None:
        # 路径损耗模型: d in km
        PL_model = lambda d: 128.1 + 37.6 * np.log10(d)

    # ----------------------------
    # 1. 生成基站位置：在大区域内随机分布
    # ----------------------------
    angles_bs = 2 * np.pi * np.random.rand(C)
    radii_bs = area_radius_km * np.sqrt(np.random.rand(C))  # 均匀分布于圆内
    cell_loc = np.array(
        [radii_bs * np.cos(angles_bs), radii_bs * np.sin(angles_bs)]
    ).T  # (C, 2)

    # ----------------------------
    # 2. 生成用户位置：每个用户属于一个基站（随机分配）
    # ----------------------------
    user_to_bs = np.random.choice(C, size=U)  # 每个用户随机分配一个服务基站
    user_loc = np.zeros((U, 2))

    for u in range(U):
        c = user_to_bs[u]
        angle = 2 * np.pi * np.random.rand()
        radius = cell_radius_km * np.sqrt(np.random.rand())  # 均匀分布于小区内
        dx = radius * np.cos(angle)
        dy = radius * np.sin(angle)
        user_loc[u, :] = cell_loc[c, :] + [dx, dy]

    # ----------------------------
    # 3. 计算距离矩阵 (U, C)
    # ----------------------------
    user_pos = user_loc[:, np.newaxis, :]  # (U, 1, 2)
    bs_pos = cell_loc[np.newaxis, :, :]  # (1, C, 2)
    dis = np.linalg.norm(user_pos - bs_pos, axis=2)  # (U, C)
    dis = np.maximum(dis, 1e-3)  # 防止距离为0

    # ----------------------------
    # 4. 慢衰落：路径损耗 + 阴影衰落
    # ----------------------------
    path_loss_db = PL_model(dis)  # (U, C)
    shadowing_db = np.random.normal(0, shadowing_std, (U, C))
    # 转换为线性域的功率增益（注意：是 /10，因为 dB 是功率单位）
    slow_linear = 10 ** (-(path_loss_db + shadowing_db) / 10)  # (U, C)

    # ----------------------------
    # 5. 快衰落：瑞利衰落 |h|^2 ~ Exp(1)
    # ----------------------------
    # 生成复高斯信道，单位平均功率
    h_fast = (np.random.randn(U, C) + 1j * np.random.randn(U, C)) / np.sqrt(2)
    fast_power = np.abs(h_fast) ** 2  # (U, C)

    # ----------------------------
    # 6. 总信道功率增益 G = |h|^2
    # ----------------------------
    H_power = slow_linear * fast_power  # (U, C)

    return H_power


def generate_channel_time_series(
    U,
    C,
    T,
    dt=1e-3,
    area_radius_km=1.0,
    cell_radius_km=0.5,
    PL_model=None,
    shadowing_std=8,
    doppler_freq=5.0,
    seed=None,
):
    """
    生成时间序列的信道增益矩阵

    Args:
        U: 用户数量
        C: 基站数量
        T: 时间步数
        dt: 时间步长 (秒)
        doppler_freq: 多普勒频率 (Hz)，控制快衰落的时间相关性
        其他参数同 generate_channel

    Returns:
        H_power_series: (T, U, C) 时间序列信道功率增益矩阵
        slow_fading: (U, C) 慢衰落分量（在整个时间序列中保持不变）
    """

    if seed is not None:
        np.random.seed(seed)

    if PL_model is None:
        # 路径损耗模型: d in km
        PL_model = lambda d: 128.1 + 37.6 * np.log10(d)

    # ----------------------------
    # 1. 生成基站位置：在大区域内随机分布
    # ----------------------------
    angles_bs = 2 * np.pi * np.random.rand(C)
    radii_bs = area_radius_km * np.sqrt(np.random.rand(C))  # 均匀分布于圆内
    cell_loc = np.array(
        [radii_bs * np.cos(angles_bs), radii_bs * np.sin(angles_bs)]
    ).T  # (C, 2)

    # ----------------------------
    # 2. 生成用户位置：每个用户属于一个基站（随机分配）
    # ----------------------------
    user_to_bs = np.random.choice(C, size=U)  # 每个用户随机分配一个服务基站
    user_loc = np.zeros((U, 2))

    for u in range(U):
        c = user_to_bs[u]
        angle = 2 * np.pi * np.random.rand()
        radius = cell_radius_km * np.sqrt(np.random.rand())  # 均匀分布于小区内
        dx = radius * np.cos(angle)
        dy = radius * np.sin(angle)
        user_loc[u, :] = cell_loc[c, :] + [dx, dy]

    # ----------------------------
    # 3. 计算距离矩阵 (U, C)
    # ----------------------------
    user_pos = user_loc[:, np.newaxis, :]  # (U, 1, 2)
    bs_pos = cell_loc[np.newaxis, :, :]  # (1, C, 2)
    dis = np.linalg.norm(user_pos - bs_pos, axis=2)  # (U, C)
    dis = np.maximum(dis, 1e-3)  # 防止距离为0

    # ----------------------------
    # 4. 慢衰落：路径损耗 + 阴影衰落（时间不变）
    # ----------------------------
    path_loss_db = PL_model(dis)  # (U, C)
    shadowing_db = np.random.normal(0, shadowing_std, (U, C))
    # 转换为线性域的功率增益
    slow_linear = 10 ** (-(path_loss_db + shadowing_db) / 10)  # (U, C)

    # ----------------------------
    # 5. 快衰落时间序列：相关的瑞利衰落
    # ----------------------------
    # 计算时间相关性系数
    # 使用 Jake's 模型：R(τ) = J0(2πfd*τ)，其中 J0 是零阶贝塞尔函数
    from scipy.special import j0

    correlation_coeff = j0(2 * np.pi * doppler_freq * dt)

    # 初始化快衰落时间序列
    H_power_series = np.zeros((T, U, C))

    # 生成初始复高斯信道
    h_complex = (np.random.randn(U, C) + 1j * np.random.randn(U, C)) / np.sqrt(2)

    for t in range(T):
        if t == 0:
            # 第一个时刻
            fast_power = np.abs(h_complex) ** 2
        else:
            # 使用一阶马尔可夫过程生成相关的快衰落
            # h[t] = ρ * h[t-1] + sqrt(1-ρ²) * w[t]
            # 其中 ρ = correlation_coeff, w[t] 是独立的复高斯噪声
            noise = (np.random.randn(U, C) + 1j * np.random.randn(U, C)) / np.sqrt(2)
            h_complex = (
                correlation_coeff * h_complex
                + np.sqrt(1 - correlation_coeff**2) * noise
            )
            fast_power = np.abs(h_complex) ** 2

        # 总信道功率增益 = 慢衰落 × 快衰落
        H_power_series[t, :, :] = slow_linear * fast_power

    return H_power_series, slow_linear


if __name__ == "__main__":
    # 示例1：单时刻信道生成
    # print("=== 单时刻信道生成示例 ===")
    # H_power = generate_channel(U=20, C=1, seed=42)
    # H_power = H_power.squeeze()
    # print(f"信道增益矩阵形状: {H_power.shape}")

    B = 4.0e4  # 带宽：40 kHz
    # # 噪声功率谱密度：热噪声 + 接收机噪声系数
    # # 热噪声：k*T = 1.38e-23 * 290 = 4.0e-21 W/Hz
    # # 考虑 7dB 噪声系数：N0 = 4.0e-21 * 10^(7/10) ≈ 2.0e-20 W/Hz
    N0 = 2.0e-20  # 更合理的噪声功率谱密度值

    # # 发射功率 P (例如，设为 0.5 W)
    P = 0.5

    # # 信道容量计算
    # # 香农公式: C = B * log2(1 + SNR), 其中 SNR = P * H / (N0 * B)
    # # N0 是噪声功率谱密度，N0*B 是总噪声功率
    # C = B * np.log2(1 + (P * H_power) / (N0 * B))

    # print("\n前5个用户的信道增益和容量:")
    # for i in range(5):
    #     print(f"用户{i+1}: 信道增益={H_power[i]:.2e}, 容量={C[i]:.2e} bps")

    # 示例2：时间序列信道生成
    print("\n=== 时间序列信道生成示例 ===")
    T = 100  # 100个时间步
    dt = 1e-3  # 1ms 时间步长
    doppler_freq = 5.0  # 5Hz 多普勒频率（对应低速移动）

    H_series, slow_fading = generate_channel_time_series(
        U=5, C=1, T=T, dt=dt, doppler_freq=doppler_freq, seed=42
    )

    print(f"时间序列信道增益形状: {H_series.shape}")
    print(f"慢衰落分量形状: {slow_fading.shape}")

    # 计算时间序列的信道容量
    C_series = B * np.log2(1 + (P * H_series.squeeze()) / (N0 * B))

    print(f"\n用户1在不同时刻的信道容量 (前10个时刻):")
    for t in range(10):
        print(f"t={t}: {C_series[t, 0]:.2e} bps")

    # 分析时间相关性
    print(f"\n时间相关性分析:")
    print(f"用户1信道增益的时间方差: {np.var(H_series[:, 0, 0]):.2e}")
    print(f"用户1信道增益的时间均值: {np.mean(H_series[:, 0, 0]):.2e}")

    # 计算相邻时刻的相关系数
    h1_t0 = H_series[:-1, 0, 0]
    h1_t1 = H_series[1:, 0, 0]
    correlation = np.corrcoef(h1_t0, h1_t1)[0, 1]
    print(f"相邻时刻信道增益的相关系数: {correlation:.3f}")

    # 理论相关系数（Jake's模型）
    from scipy.special import j0

    theoretical_corr = j0(2 * np.pi * doppler_freq * dt)
    print(f"理论相关系数: {theoretical_corr:.3f}")
