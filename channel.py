import numpy as np


def generate_channel(
    U,
    C,
    area_radius_km=1.0,
    cell_radius_km=0.5,
    PL_model=None,
    shadowing_std=8,
    seed=None,
):

    if seed is not None:
        np.random.seed(seed)

    if PL_model is None:
        # 路径损耗模型: d in km
        PL_model = lambda d: 128.1 + 37.6 * np.log10(d)

    # ----------------------------
    # 1. 生成基站位置：在大区域内随机分布
    # ----------------------------
    angles_bs = 2 * np.pi * np.random.rand(C)
    radii_bs = area_radius_km * np.sqrt(np.random.rand(C))  # 均匀分布于圆内
    cell_loc = np.array(
        [radii_bs * np.cos(angles_bs), radii_bs * np.sin(angles_bs)]
    ).T  # (C, 2)

    # ----------------------------
    # 2. 生成用户位置：每个用户属于一个基站（随机分配）
    # ----------------------------
    user_to_bs = np.random.choice(C, size=U)  # 每个用户随机分配一个服务基站
    user_loc = np.zeros((U, 2))

    for u in range(U):
        c = user_to_bs[u]
        angle = 2 * np.pi * np.random.rand()
        radius = cell_radius_km * np.sqrt(np.random.rand())  # 均匀分布于小区内
        dx = radius * np.cos(angle)
        dy = radius * np.sin(angle)
        user_loc[u, :] = cell_loc[c, :] + [dx, dy]

    # ----------------------------
    # 3. 计算距离矩阵 (U, C)
    # ----------------------------
    user_pos = user_loc[:, np.newaxis, :]  # (U, 1, 2)
    bs_pos = cell_loc[np.newaxis, :, :]  # (1, C, 2)
    dis = np.linalg.norm(user_pos - bs_pos, axis=2)  # (U, C)
    dis = np.maximum(dis, 1e-3)  # 防止距离为0

    # ----------------------------
    # 4. 慢衰落：路径损耗 + 阴影衰落
    # ----------------------------
    path_loss_db = PL_model(dis)  # (U, C)
    shadowing_db = np.random.normal(0, shadowing_std, (U, C))
    # 转换为线性域的功率增益（注意：是 /10，因为 dB 是功率单位）
    slow_linear = 10 ** (-(path_loss_db + shadowing_db) / 10)  # (U, C)

    # ----------------------------
    # 5. 快衰落：瑞利衰落 |h|^2 ~ Exp(1)
    # ----------------------------
    # 生成复高斯信道，单位平均功率
    h_fast = (np.random.randn(U, C) + 1j * np.random.randn(U, C)) / np.sqrt(2)
    fast_power = np.abs(h_fast) ** 2  # (U, C)

    # ----------------------------
    # 6. 总信道功率增益 G = |h|^2
    # ----------------------------
    H_power = slow_linear * fast_power  # (U, C)

    return H_power


if __name__ == "__main__":
    # 示例1：固定数量
    H_power = generate_channel(U=20, C=1, seed=42)
    print(H_power.shape)

    B = 4.0e4  # 带宽：40 kHz
    # 噪声功率谱密度：热噪声 + 接收机噪声系数
    # 热噪声：k*T = 1.38e-23 * 290 = 4.0e-21 W/Hz
    # 考虑 7dB 噪声系数：N0 = 4.0e-21 * 10^(7/10) ≈ 2.0e-20 W/Hz
    N0 = 2.0e-20  # 更合理的噪声功率谱密度值

    # 发射功率 P (例如，设为 1 W)
    P = 0.5

    # 信道容量计算
    # 香农公式: C = B * log2(1 + SNR), 其中 SNR = P * H / (N0 * B)
    # N0 是噪声功率谱密度，N0*B 是总噪声功率
    C = B * np.log2(1 + (P * H_power) / (N0 * B))
    # 答应出小数点5位
    for i in range(20):
        print(f"{H_power[i][0]:.10f}")
        print(f"用户{i+1}的信道容量: {C[i][0]:.5f} bps")
