import logging
import os
import pickle
from typing import Dict, List

from entity.entitys import TaskConfig
from multi_task_client import MultiTaskClient


class SimilarityServer:

    def __init__(
        self,
        similarity_threshold: float = 0.5,
        task_configs: Dict[str, TaskConfig] = None,
    ):
        self.logger = logging.getLogger(f"SimilarityServer")

        self.client_similarity = {}
        self.similarity_threshold = similarity_threshold
        self.task_configs = task_configs

        # Create cache directory if it doesn't exist
        os.makedirs(self.similarity_cache_dir, exist_ok=True)
        # Similarity cache settings
        self.similarity_cache_dir = "cache/similarity"
        self.similarity_cache_file = os.path.join(
            self.similarity_cache_dir, f"similarity_server.pkl"
        )
        self.logger.info(f"Similarity cache file: {self.similarity_cache_file}")

    def add_client_similarity(self, client_id, similarity_matrix, distance_matrix):
        """Add client similarity to server"""
        self.logger.info(f"Adding client {client_id} similarity to server")
        self.client_similarity[client_id] = {
            "similarity_matrix": similarity_matrix,
            "distance_matrix": distance_matrix,
        }

    def _load_similarity_from_cache(self) -> bool:
        """Load similarity matrix from cache file if it exists"""
        if not self.similarity_cache_file or not os.path.exists(
            self.similarity_cache_file
        ):
            self.logger.info("No similarity cache file found")
            return False

        try:
            with open(self.similarity_cache_file, "rb") as f:
                cache_data = pickle.load(f)

            self.similarity_matrix = cache_data["similarity_matrix"]
            self.distance_matrix = cache_data["distance_matrix"]
            self.similarity_computed = True

            # Determine task grouping based on loaded similarity
            self._determine_task_grouping()

            self.logger.info(
                f"Loaded similarity matrix from cache: {self.similarity_cache_file}"
            )
            self.logger.info(f"Shared task groups: {self.shared_tasks}")
            self.logger.info(f"Independent tasks: {self.independent_tasks}")

            return True

        except Exception as e:
            self.logger.error(f"Failed to load similarity cache: {e}")
            return False

    def _save_similarity_to_cache(self):
        """Save similarity matrix to cache file"""
        if not self.similarity_cache_file:
            return

        try:
            cache_data = {
                "similarity_matrix": self.similarity_matrix,
                "distance_matrix": self.distance_matrix,
                "task_configs": {
                    name: {
                        "name": config.name,
                        "task_type": config.task_type.value,
                        "model_type": config.model_type,
                    }
                    for name, config in self.task_configs.items()
                },
                "shared_tasks": self.shared_tasks,
                "independent_tasks": self.independent_tasks,
                "similarity_threshold": self.similarity_threshold,
            }

            with open(self.similarity_cache_file, "wb") as f:
                pickle.dump(cache_data, f)

            self.logger.info(
                f"Saved similarity matrix to cache: {self.similarity_cache_file}"
            )

        except Exception as e:
            self.logger.error(f"Failed to save similarity cache: {e}")

    def _determine_task_grouping(self, clients: List[MultiTaskClient]):
        """Determine task grouping based on similarity matrix"""
        if self.similarity_matrix is None:
            self.independent_tasks = list(self.task_configs.keys())
            self.shared_tasks = []
            return

        task_names = list(self.task_configs.keys())
        n_tasks = len(task_names)
        visited = [False] * n_tasks

        self.shared_tasks = []
        self.independent_tasks = []

        for i in range(n_tasks):
            if visited[i]:
                continue

            # Find similar tasks
            similar_group = [task_names[i]]
            visited[i] = True

            for j in range(i + 1, n_tasks):
                if (
                    not visited[j]
                    and self.similarity_matrix[i, j] > self.similarity_threshold
                ):
                    similar_group.append(task_names[j])
                    visited[j] = True

            # Group tasks
            if len(similar_group) > 1:
                self.shared_tasks.append(similar_group)
            else:
                self.independent_tasks.extend(similar_group)
