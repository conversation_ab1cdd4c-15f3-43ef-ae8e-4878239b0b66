"""
Multi-Task Federated Learning Server

This module extends the existing federated learning server to handle multiple tasks
with MADDPG resource allocation and NS3 network simulation integration.
"""

import logging
import os
import sys
import numpy as np
import time
from typing import Dict, List, Any, Tuple
import multiprocessing as mp
from multiprocessing import Pool, Manager
import pickle
import copy

import torch

# Import our multi-task components
from channel import generate_channel_time_series
from multi_task_client import MultiTaskClient
from entity.entitys import (
    ExperimentConfig,
    ResourceAllocation,
    NetworkMetrics,
    TaskConfig,
    TaskType,
    MultiTaskFLConfig,
)
from maddpg.maddpg_allocator import MADDPGResourceAllocator
from torch.utils.tensorboard import SummaryWriter
from data_preprocessing.cifar10.data_loader import load_partition_data_cifar10
from data_preprocessing.cifar100.data_loader import load_partition_data_cifar100
from data_preprocessing.SVHN.data_loader import load_partition_data_SVHN
from data_preprocessing.MNIST.data_loader import (
    load_partition_data_mnist_custom,
    load_partition_data_mnist_fast,
)
from data_preprocessing.FashionMNIST.data_loader import (
    load_partition_data_FashionMNIST_custom,
)

from similarity_server import SimilarityServer


def train_client_worker(client_data):
    """
    多进程客户端训练工作函数

    Args:
        client_data: 包含客户端信息和训练参数的字典

    Returns:
        训练报告
    """
    try:
        # 解包客户端数据
        client_id = client_data["client_id"]
        client_state = client_data["client_state"]
        global_model_weights = client_data.get("global_model_weights", None)
        training_config = client_data.get("training_config", {})

        # 重建客户端对象
        client = MultiTaskClient.from_state(client_state)

        # 接收全局模型权重
        if global_model_weights:
            client.receive_global_model(global_model_weights)

        # 执行本地训练
        training_start_time = time.time()
        local_training_metrics = client.run()
        training_duration = time.time() - training_start_time

        # 创建训练报告
        report = {
            "client_id": client_id,
            "success": True,
            "training_metrics": local_training_metrics,
            "training_duration": training_duration,
            "model_weights": client.get_model_weights(),
            "resource_utilization": _get_client_resource_utilization(client),
        }

        return report

    except Exception as e:
        # 抛出异常
        raise e


def _get_client_resource_utilization(client):
    """获取客户端资源利用率信息"""
    try:
        if not client.current_allocation:
            raise

        return {
            "total_compute": sum(client.current_allocation.q_ij.values()),
            "total_power": sum(client.current_allocation.P_ij.values()),
            "task_allocations": {
                "compute": dict(client.current_allocation.q_ij),
                "power": dict(client.current_allocation.P_ij),
                "selection": dict(client.current_allocation.select_ij),
                "priority": dict(client.current_allocation.Task_ij),
            },
        }
    except Exception:
        raise


def load_data(config: ExperimentConfig, dataset_name: str, logger):
    # check if the centralized training is enabled
    centralized = True if config.clients.total == 1 else False

    # check if the full-batch training is enabled
    args_batch_size = config.multi_task.tasks[dataset_name].batch_size
    if config.multi_task.tasks[dataset_name].batch_size <= 0:
        full_batch = True
        config.multi_task.tasks[dataset_name].batch_size = 128  # temporary batch size
    else:
        full_batch = False

    if dataset_name in ["mnist", "mn"]:
        logger.info("load_data. dataset_name = %s" % dataset_name)
        config.multi_task.tasks[dataset_name].data_path = "./data/MNIST"

        if "cnn" not in config.multi_task.tasks[dataset_name].model_type:
            (
                train_data_num,
                test_data_num,
                train_data_global,
                test_data_global,
                train_data_local_num_dict,
                train_data_local_dict,
                test_data_local_dict,
                class_num,
            ) = load_partition_data_mnist_fast(
                dataset_name,
                config.multi_task.tasks[dataset_name].data_path,
                config.multi_task.tasks[dataset_name].partition_method,
                config.multi_task.tasks[dataset_name].partition_alpha,
                config.clients.total,
                config.clients.total,
                config.multi_task.tasks[dataset_name].batch_size,
            )
        else:
            (
                train_data_num,
                test_data_num,
                train_data_global,
                test_data_global,
                train_data_local_num_dict,
                train_data_local_dict,
                test_data_local_dict,
                class_num,
            ) = load_partition_data_mnist_custom(
                dataset_name,
                config.multi_task.tasks[dataset_name].data_path,
                config.multi_task.tasks[dataset_name].partition_method,
                config.multi_task.tasks[dataset_name].partition_alpha,
                config.clients.total,
                config.clients.total,
                config.multi_task.tasks[dataset_name].batch_size,
            )
    elif dataset_name in ["fashionmnist", "FashionMNIST", "fmn", "fmnist"]:
        logger.info("load_data. dataset_name = %s" % dataset_name)
        config.multi_task.tasks[dataset_name].data_path = "./data/FashionMNIST"

        (
            train_data_num,
            test_data_num,
            train_data_global,
            test_data_global,
            train_data_local_num_dict,
            train_data_local_dict,
            test_data_local_dict,
            class_num,
        ) = load_partition_data_FashionMNIST_custom(
            dataset_name,
            config.multi_task.tasks[dataset_name].data_path,
            config.multi_task.tasks[dataset_name].partition_method,
            config.multi_task.tasks[dataset_name].partition_alpha,
            config.clients.total,
            config.clients.total,
            config.multi_task.tasks[dataset_name].batch_size,
        )

        """
        For shallow NN or linear models, 
        we uniformly sample a fraction of clients each round (as the original FedAvg paper)
        """

    else:
        if dataset_name in ["cifar10", "cf10"]:
            data_loader = load_partition_data_cifar10
        elif dataset_name in ["cifar100", "cf100"]:
            data_loader = load_partition_data_cifar100
        elif dataset_name in ["svhn", "SVHN", "sn"]:
            logger.info("load_data. dataset_name = %s" % dataset_name)
            config.multi_task.tasks[dataset_name].data_path = "./data/SVHN"
            data_loader = load_partition_data_SVHN
        else:
            data_loader = load_partition_data_cifar10
            logger.warning("Unknown dataset! (dataset_name = %s)" % dataset_name)
        (
            train_data_num,
            test_data_num,
            train_data_global,
            test_data_global,
            train_data_local_num_dict,
            train_data_local_dict,
            test_data_local_dict,
            class_num,
        ) = data_loader(
            dataset_name,
            config.multi_task.tasks[dataset_name].data_path,
            config.multi_task.tasks[dataset_name].partition_method,
            config.multi_task.tasks[dataset_name].partition_alpha,
            config.clients.total,
            config.multi_task.tasks[dataset_name].batch_size,
        )

    if centralized:
        train_data_local_num_dict = {
            0: sum(
                user_train_data_num
                for user_train_data_num in train_data_local_num_dict.values()
            )
        }
        train_data_local_dict = {
            0: [
                batch
                for cid in sorted(train_data_local_dict.keys())
                for batch in train_data_local_dict[cid]
            ]
        }
        test_data_local_dict = {
            0: [
                batch
                for cid in sorted(test_data_local_dict.keys())
                for batch in test_data_local_dict[cid]
            ]
        }
        # config.clients.total = 1

    if full_batch:
        train_data_global = combine_batches(train_data_global)
        test_data_global = combine_batches(test_data_global)
        train_data_local_dict = {
            cid: combine_batches(train_data_local_dict[cid])
            for cid in train_data_local_dict.keys()
        }
        test_data_local_dict = {
            cid: combine_batches(test_data_local_dict[cid])
            for cid in test_data_local_dict.keys()
        }
        # config.multi_task.tasks[dataset_name].batch_size = args_batch_size

    dataset = [
        train_data_num,
        test_data_num,
        train_data_global,
        test_data_global,
        train_data_local_num_dict,
        train_data_local_dict,
        test_data_local_dict,
        class_num,
    ]
    return dataset


def combine_batches(batches):
    full_x = torch.from_numpy(np.asarray([])).float()
    full_y = torch.from_numpy(np.asarray([])).long()
    for batched_x, batched_y in batches:
        full_x = torch.cat((full_x, batched_x), 0)
        full_y = torch.cat((full_y, batched_y), 0)
    return [(full_x, full_y)]


def calculate_sla_violation_rate(y_pred, alpha, beta):
    """
    计算SLA违反率

    参数:
    y_pred -- 预测值，形状为 (D_n,) 的数组，其中 D_n 是样本数
    alpha -- SLA的下限 [
    beta -- SLA的上限 ]

    返回:
    SLA违反率 ν_k,n
    """
    # 计算是否违反SLA的指示函数
    violation_indicator = np.logical_or(y_pred < alpha, y_pred > beta).astype(int)

    # 计算违反率
    sla_violation_rate = np.sum(violation_indicator) / len(y_pred)

    return sla_violation_rate


class MultiTaskFederatedServer:
    """
    Multi-task federated learning server with MADDPG resource allocation
    and NS3 network simulation integration
    """

    def __init__(self, config: ExperimentConfig, tensorboard: bool = False):

        self.logger = logging.getLogger(__name__)

        # Initialize base server (but skip automatic boot)
        self.config = config
        # Multi-task specific configuration
        self.mt_config = MultiTaskFLConfig()
        if hasattr(config, "multi_task"):
            self.mt_config = config.multi_task

        self.tensorboard = tensorboard
        if self.tensorboard:
            writer_path = f"writer/tasks_{self.mt_config.num_task}/num_clients_{self.mt_config.num_clients}/{config.current_time}"
            self.logger.info(f"TensorBoard logs will be saved to: {writer_path}")
            # 创建 writer
            if not os.path.exists(writer_path):
                os.makedirs(writer_path)
            self.writer = SummaryWriter(log_dir=writer_path)

        # Default task configurations
        if self.mt_config.tasks is None:
            self.mt_config.tasks = {
                "mnist": TaskConfig(
                    "mnist", TaskType.IMAGE_CLASSIFICATION, "data/MNIST", "cnn"
                ),
                "cifar10": TaskConfig(
                    "cifar10",
                    TaskType.IMAGE_CLASSIFICATION,
                    "data/cifar-10-batches-py",
                    "resnet",
                ),
                "airquality": TaskConfig(
                    "airquality", TaskType.TIME_SERIES, "data/air_quality", "gru"
                ),
            }

        # Initialize clients with multi-task configurations
        self.clients = []

        # Training state
        self.current_round = 0
        self.round_metrics: List[Dict[str, Any]] = []
        self.episode_metrics: Dict[str, List[Dict[str, Any]]] = {}

        self.channel_gain, _ = generate_channel_time_series(
            U=self.mt_config.num_clients,
            C=self.mt_config.num_task,
            T=100,  # 100个时间步
            dt=1e-3,  # 时间步长
            doppler_freq=5.0,  # 多普勒频率
            seed=42,
        )
        self.bandwidth = 18e4
        N0 = 1e-3 * 10 ** (-174 / 10)
        F = 10 ** (3 / 10)
        # noise = B * N0 * F
        self.noise = self.bandwidth * N0 * F
        self.number_of_local_epochs = self.mt_config.clients_per_round
        self.cpu_freq = (
            np.random.uniform(2.0, 4.0, size=(self.mt_config.num_clients)) * 1.0e09
        )  # unit: Hz
        self.cpu_cycles = 20
        self.capacitance_coefficient = 1.0e-28
        self.min_power = 1.0e-03  # unit: W
        self.max_power = 1.5  # unit: W

        self.dataset = {}
        for dataset_name in self.mt_config.tasks.keys():
            self.logger.info(f"Loading data for {dataset_name}")
            self.dataset[dataset_name] = load_data(config, dataset_name, self.logger)

        # init client；make_clients()会进行相似性判断
        self.similarity_server = SimilarityServer(
            self.mt_config.similarity_threshold, self.mt_config.tasks
        )

        self.make_clients(self.mt_config.num_clients)  # reset()里会调用

        # [select_ij, power_ij, task_ij, q_ij, energy_use_ij, delay_ij, accuracy_ij]
        # self.state_dim = 7 * num_clients * num_tasks
        num_task = len(self.similarity_server.independent_tasks) + len(
            self.similarity_server.shared_tasks
        )
        config.maddpg_config.state_dim = 7 * num_task
        config.maddpg_config.action_dim = 4 * num_task
        self.mt_config.num_task = num_task

        self._update_client_num_task()

        # MADDPG resource allocator
        self.maddpg_allocator = MADDPGResourceAllocator(
            num_clients=self.mt_config.num_clients,
            num_tasks=num_task,
            maddpg_config=config.maddpg_config,
        )

        self.last_state = None
        self.logger.info("Multi-Task Federated Server initialized")

    def _update_client_num_task(self):
        for client in self.clients:
            client.num_task = self.mt_config.num_task

    def make_clients(self, num_clients):
        """Create multi-task clients"""
        self.logger.info(f"Creating {num_clients} multi-task clients...")

        # Create multi-task clients
        clients = []
        for client_id in range(num_clients):
            # Create multi-task client
            client = MultiTaskClient(
                self.config.device,
                client_id,
                self.mt_config.tasks,
                self.mt_config.num_task,
                self.dataset,
                self.mt_config.mu,
            )

            # Compute task similarity (will be cached after first computation)
            similarity_matrix, distance_matrix = client.compute_task_similarity(
                force_recompute=False
            )
            self.similarity_server.add_client_similarity(
                client_id, similarity_matrix, distance_matrix
            )

            clients.append(client)

        # Determine task grouping based on similarity matrix
        self.similarity_server._determine_task_grouping(clients, force_recompute=True)

        self.clients = clients
        self.logger.info(f"Created {len(clients)} multi-task clients")

    def run_federated_round(
        self, round_num: int, states: Any
    ) -> Tuple[Dict[str, Any], Any]:
        """Run a single federated learning round with MADDPG and NS3 integration

        Workflow:
        1. MADDPG资源分配 → 2. 客户端训练 → 3. 服务器聚合 → 4. MADDPG奖励反馈
        """
        # TODO run_federated_round
        self.logger.info(f"Starting federated round {round_num}")

        available_clients = self._get_available_clients()
        self.logger.info(
            f"Available clients for this round: {[c.client_id for c in available_clients]}"
        )

        resource_allocations = self.maddpg_allocator.get_resource_allocations(states)
        self.logger.info(
            f"Current_round {self.current_round} Step 1: MADDPG resource allocation completed"
        )

        selected_clients = self._select_clients_by_maddpg(
            available_clients, resource_allocations
        )

        self.logger.info(
            f"MADDPG selected {len(selected_clients)} clients: {[c.client_id for c in selected_clients]}"
        )

        for client in selected_clients:
            if client.client_id in resource_allocations:
                client.set_resource_allocation(resource_allocations[client.client_id])

        self.logger.info(
            f"Current_round {self.current_round} Step 2: Starting network simulation for model transmission..."
        )
        network_metrics = self._simulate_model_transmission(selected_clients)

        for client in selected_clients:
            if client.client_id in network_metrics:
                client.set_network_metrics(network_metrics[client.client_id])

        self.logger.info(
            f"Current_round {self.current_round} Step 3: Starting client training with allocated resources..."
        )
        training_reports = self._run_client_training(selected_clients)

        self.logger.info(
            f"Current_round {self.current_round} Step 4: Aggregating models at server..."
        )
        aggregated_weights = self._aggregate_models(training_reports)

        rewards = self._calculate_maddpg_rewards(
            available_clients=available_clients,
            selected_clients=selected_clients,
            reports=training_reports,
            network_metrics=network_metrics,
        )
        self.logger.info(f"rewards: {rewards}")
        self.logger.info(
            f"Current_round {self.current_round} Step 5: Updating MADDPG with performance rewards..."
        )
        next_states = self._collect_next_states(selected_clients)
        self.last_state = next_states

        self.maddpg_allocator.update_with_rewards(rewards, next_states)

        round_metrics = self._collect_round_metrics(
            round_num,
            selected_clients,
            training_reports,
            network_metrics,
            rewards,
        )
        self.round_metrics.append(round_metrics)

        self.logger.info(f"Round {round_num} completed ")
        self.logger.info(f"Workflow: MADDPG → Training → Aggregation → Rewards")

        return round_metrics, next_states

    def _get_available_clients(self) -> List[MultiTaskClient]:
        """Get all available clients for MADDPG decision making"""
        # Return all clients - MADDPG will decide which ones to select
        return self.clients

    def _select_clients_by_maddpg(
        self,
        available_clients: List[MultiTaskClient],
        resource_allocations: Dict[int, ResourceAllocation],
    ) -> List[MultiTaskClient]:
        """
        Select clients based on MADDPG decisions (select_ij values)

        如果客户端的allocation.select_ij中有任何一个任务j>0，就说明该客户端参与训练
        """
        selected_clients = []

        for client in available_clients:
            if client.client_id in resource_allocations:
                allocation = resource_allocations[client.client_id]

                # 检查是否有任何任务的select_ij值大于0
                has_selected_task = False
                selected_tasks = []

                for task_name, select_value in allocation.select_ij.items():
                    if select_value >= self.config.maddpg_config.action_threshold:
                        has_selected_task = True
                        selected_tasks.append(f"{task_name}({select_value:.3f})")

                if has_selected_task:
                    selected_clients.append(client)
                    self.logger.debug(
                        f"Client {client.client_id} selected for tasks: {', '.join(selected_tasks)}"
                    )
                else:
                    self.logger.debug(
                        f"Client {client.client_id} not selected - no tasks with select_ij > 0"
                    )
            else:
                self.logger.warning(
                    f"Client {client.client_id} has no resource allocation"
                )

        # # 确保至少有一个客户端被选中（回退机制）
        # if not selected_clients and available_clients:
        #     # 选择具有最高总选择概率的客户端
        #     def get_selection_score(client):
        #         if client.client_id in resource_allocations:
        #             allocation = resource_allocations[client.client_id]
        #             return sum(allocation.select_ij.values())
        #         return 0

        #     best_client = max(available_clients, key=get_selection_score)
        #     selected_clients.append(best_client)

        #     # 记录回退选择的详细信息
        #     if best_client.client_id in resource_allocations:
        #         allocation = resource_allocations[best_client.client_id]
        #         task_scores = [
        #             f"{task}({score:.3f})"
        #             for task, score in allocation.select_ij.items()
        #         ]
        #         self.logger.info(
        #             f"Fallback: Selected client {best_client.client_id} with task scores: {', '.join(task_scores)}"
        #         )
        #     else:
        #         self.logger.info(
        #             f"Fallback: Selected client {best_client.client_id} (no allocation available)"
        #         )

        self.logger.info(
            f"MADDPG client selection completed: {len(selected_clients)}/{len(available_clients)} clients selected"
        )
        return selected_clients

    def _collect_next_states(self, clients: List[MultiTaskClient]) -> List[List[float]]:
        """Collect current states from clients for MADDPG"""
        states = []
        total_clients = (
            self.mt_config.num_clients
        )  # Use mt_config instead of config.clients.total
        re_set = set()

        tmp_state = {}
        for client in clients:
            re_set.add(client.client_id)
            tmp_state[client.client_id] = (
                client.get_state_for_maddpg()
            )  # Now returns List[float]

        # Fill missing clients with default states
        for i in range(total_clients):
            if i not in re_set:
                if states:
                    # Use the last valid state as template
                    old_state = states[-1]
                    # Create a copy of the last state (all values are already floats)
                    state = old_state.copy()
                else:
                    # Default state if no clients available
                    # [select_ij, power_ij, task_ij, q_ij, energy_use_ij, delay_ij, accuracy_ij]
                    state = np.ones(7 * self.mt_config.num_task) * 0.5

                states.append(state)
                self.logger.debug(
                    f"Added default state for missing client {i}: {state}"
                )
            else:
                state = tmp_state[i]
                states.append(state)
                self.logger.debug(f"Client {i} state: {state}")

        return states

    def _simulate_model_transmission(
        self, clients: List[MultiTaskClient]
    ) -> Dict[int, List[NetworkMetrics]]:
        # TODO 仿真模型传输
        network_metrics = {}
        for client in clients:
            i = client.client_id
            network_metrics[i] = []
            # 对client的current_allocation的Task_ij进行排序，以优先级高低进行传输 (Task_ij越大，优先级越高)
            sorted_tasks = sorted(
                client.current_allocation.Task_ij.items(),
                key=lambda item: item[1],
                reverse=True,
            )
            for task_name, _ in sorted_tasks:
                if (
                    client.current_allocation.select_ij[task_name]
                    >= self.config.maddpg_config.action_threshold
                ):
                    j = int(task_name[5:])
                    power_sla_rate = calculate_sla_violation_rate(
                        y_pred=np.array([client.current_allocation.P_ij[task_name]]),
                        alpha=self.min_power,
                        beta=self.max_power,
                    )

                    client.current_allocation.P_ij[task_name] = np.clip(
                        client.current_allocation.P_ij[task_name],
                        self.min_power,
                        self.max_power,
                    )

                    # SNR = P * H / (B * N0 * F)
                    snr = (
                        client.current_allocation.P_ij[task_name]
                        * self.channel_gain[self.current_round, i, j]
                    ) / self.noise
                    # Shannon capacity formula (单位是比特/秒)
                    up_rate = self.bandwidth * np.log2(1 + snr)

                    # File size (单位是字节)
                    file_size = self._calculate_file_size(task_id=j, client_id=i)

                    ##### time
                    # T = S/r
                    time_up = file_size * 8 / up_rate

                    # 计算时间应该基于实际训练数据量，而不是模型文件大小
                    data_samples = self._get_client_training_samples(client, task_name)
                    time_cmp = (
                        self.number_of_local_epochs
                        * self.cpu_cycles
                        * data_samples
                        / self.cpu_freq[i]
                    )

                    ##### energy
                    # E = P * T
                    energy_up = client.current_allocation.P_ij[task_name] * time_up
                    # 计算能耗也应该基于实际训练数据量
                    energy_cmp = (
                        self.number_of_local_epochs
                        * self.capacitance_coefficient
                        * self.cpu_cycles
                        * data_samples
                        * (self.cpu_freq[i] ** 2)
                    )
                    # 顺序执行，后面的任务需要等待前面的任务完成，所以需要加上前面的任务延迟
                    length = len(network_metrics[i])
                    last_task_delay = (
                        network_metrics[i][length - 1].delay if length > 0 else 0
                    )
                    delay = time_up + time_cmp + last_task_delay
                    # 如果delay 数值变成了无穷大，那抛出错误
                    if delay == float("inf"):
                        raise ValueError(
                            f"Delay is inf for client {i}, task {task_name}"
                        )
                    network_metrics[i].append(
                        NetworkMetrics(
                            client_id=i,
                            task_id=j,
                            task_name=task_name,
                            power=client.current_allocation.P_ij[task_name],
                            energy_use=energy_up + energy_cmp,
                            energy_up=energy_up,
                            energy_cmp=energy_cmp,
                            energy_down=0.0,
                            delay=delay,
                            up_time=time_up,
                            cmp_time=time_cmp,
                            down_time=0.0,
                            ph_divideby_n0=snr * self.bandwidth,
                            power_sla_rate=power_sla_rate,
                        )
                    )
        return network_metrics

    def _calculate_file_size(self, task_id: int, client_id: int) -> float:
        """
        Calculate file size based on actual model parameters that need to be uploaded

        Args:
            task_id: Task ID
            client_id: Client ID

        Returns:
            File size in bytes (模型参数的实际大小)
        """
        # 根据任务ID获取对应的模型类型和参数数量
        # task_names = list(self.mt_config.tasks.keys())
        if len(self.similarity_server.shared_tasks) > 0:
            task_names = ["share"]
        else:
            task_names = []
        task_names.extend(self.similarity_server.independent_tasks)

        if task_id < len(task_names):
            task_name = task_names[task_id]
            if task_name == "share":
                task_name = "mnist"  # 临时方案，后续需要修改
            task_config = self.mt_config.tasks[task_name]
            model_type = task_config.model_type
        else:
            # 默认情况
            model_type = "cnn"
            task_name = "default"

        # 计算不同模型的参数数量
        if model_type == "cnn":
            if "mnist" in task_name.lower():
                # MNIST CNN模型参数计算 (基于 multi_task_client.py 中的实际结构)
                # nn.Conv2d(1, 32, 3, padding=1): weights=(1*32*3*3)=288, bias=32 → 320
                # nn.Conv2d(32, 64, 3, padding=1): weights=(32*64*3*3)=18432, bias=64 → 18496
                # nn.Linear(64*7*7, 128): weights=(64*7*7*128)=401408, bias=128 → 401536
                # nn.Linear(128, 10): weights=(128*10)=1280, bias=10 → 1290
                conv1_params = 1 * 32 * 3 * 3 + 32  # 320
                conv2_params = 32 * 64 * 3 * 3 + 64  # 18496
                fc1_params = 64 * 7 * 7 * 128 + 128  # 401536
                fc2_params = 128 * 10 + 10  # 1290
                total_params = (
                    conv1_params + conv2_params + fc1_params + fc2_params
                )  # 421642

            elif "cifar" in task_name.lower():
                # CIFAR CNN模型参数计算 (基于 multi_task_client.py 中的实际结构)
                # nn.Conv2d(3, 32, 3, padding=1): weights=(3*32*3*3)=864, bias=32 → 896
                # nn.Conv2d(32, 64, 3, padding=1): weights=(32*64*3*3)=18432, bias=64 → 18496
                # nn.Conv2d(64, 128, 3, padding=1): weights=(64*128*3*3)=73728, bias=128 → 73856
                # nn.Linear(128*4*4, 256): weights=(128*4*4*256)=524288, bias=256 → 524544
                # nn.Linear(256, 10): weights=(256*10)=2560, bias=10 → 2570
                conv1_params = 3 * 32 * 3 * 3 + 32  # 896
                conv2_params = 32 * 64 * 3 * 3 + 64  # 18496
                conv3_params = 64 * 128 * 3 * 3 + 128  # 73856
                fc1_params = 128 * 4 * 4 * 256 + 256  # 524544
                fc2_params = 256 * 10 + 10  # 2570
                total_params = (
                    conv1_params + conv2_params + conv3_params + fc1_params + fc2_params
                )  # 620362

            else:
                # 默认CNN (基于通用3通道结构)
                # 估算参数数量
                total_params = 500000  # 约50万参数

        elif model_type == "resnet":
            # ResNet18 参数数量（精确计算）
            # 基于 torchvision.models.resnet18 的实际结构
            # 包括所有卷积层、批归一化层、全连接层
            # 最后的 fc 层被替换为 Linear(512, 10)
            total_params = 11689512  # ResNet18的标准参数数量
            # 调整最后一层：原来是 Linear(512, 1000)，现在是 Linear(512, 10)
            # 减少：512 * 1000 + 1000 = 513000
            # 增加：512 * 10 + 10 = 5130
            total_params = total_params - 513000 + 5130  # 11181642

        elif model_type == "gru":
            # GRU模型参数计算 (基于 multi_task_client.py 中的实际结构)
            # nn.GRU(input_size=1, hidden_size=64, batch_first=True)
            # GRU参数计算：3 * (input_size * hidden_size + hidden_size * hidden_size + hidden_size)
            # 3个门：重置门、更新门、新门，每个门都有输入权重、隐藏权重、偏置
            input_size = 1
            hidden_size = 64
            gru_params = 3 * (
                input_size * hidden_size + hidden_size * hidden_size + hidden_size
            )
            gru_params = 3 * (
                1 * 64 + 64 * 64 + 64
            )  # 3 * (64 + 4096 + 64) = 3 * 4224 = 12672

            # nn.Linear(64, 32): weights=(64*32)=2048, bias=32 → 2080
            fc1_params = 64 * 32 + 32  # 2080

            # nn.Linear(32, 1): weights=(32*1)=32, bias=1 → 33
            fc2_params = 32 * 1 + 1  # 33

            total_params = (
                gru_params + fc1_params + fc2_params
            )  # 12672 + 2080 + 33 = 14785

        else:
            # 默认参数数量
            total_params = 100000  # 10万参数

        # 每个参数通常是32位浮点数 = 4字节
        bytes_per_param = 4
        file_size_bytes = total_params * bytes_per_param

        file_size_bytes = 5.0e4  # TODO 固定模型大小

        # 可以根据客户端ID添加一些变化（不同客户端可能有略微不同的模型大小）
        client_variation = 1.0 + (client_id * 0.01)  # 每个客户端增加1%的变化
        file_size_bytes = int(file_size_bytes * client_variation)

        self.logger.info(
            f"Client {client_id}, Task {task_id} ({task_name}, {model_type}): {total_params:,} params, "
            f"{file_size_bytes:,} bytes ({file_size_bytes/1024/1024:.2f} MB)"
        )
        return file_size_bytes

    def _get_client_training_samples(
        self, client: MultiTaskClient, task_name: str
    ) -> int:
        """
        获取客户端指定任务的训练样本数量

        Args:
            client: 客户端对象
            task_name: 任务名称 (可能是 'task_0', 'task_1' 格式)

        Returns:
            训练样本数量
        """
        actual_task_name = self._convert_task_name_to_actual(task_name)
        if type(actual_task_name) == list:
            total_samples = 0
            for shared_task_name in actual_task_name:
                train_loader, _ = client.task_data_loaders[shared_task_name]
                total_samples += len(train_loader.dataset)
            self.logger.debug(
                f"Client {client.client_id}, Task {task_name} ({actual_task_name}): {total_samples} training samples"
            )
            return total_samples
        elif type(actual_task_name) == str:
            train_loader, _ = client.task_data_loaders[actual_task_name]
            # 获取训练数据集的样本数量
            samples = len(train_loader.dataset)
            self.logger.debug(
                f"Client {client.client_id}, Task {task_name} ({actual_task_name}): {samples} training samples"
            )
            return samples
        else:
            raise ValueError(f"Unknown task name format: {task_name}")

    def _convert_task_name_to_actual(self, task_name: str):
        """
        将任务名称从 'task_0', 'task_1' 格式转换为实际任务名称

        Args:
            task_name: 格式化的任务名称 (如 'task_0', 'task_1')

        Returns:
            实际的任务名称 (如 'mnist', 'cifar10', 'airquality')
        """
        idx = int(task_name.split("_")[1])
        if idx < len(self.similarity_server.shared_tasks):
            return self.similarity_server.shared_tasks[idx]
        else:
            return self.similarity_server.independent_tasks[
                idx - len(self.similarity_server.shared_tasks)
            ]

    def _run_client_training(self, clients: List[MultiTaskClient]) -> List[Any]:
        """
        执行真正的联邦学习客户端训练流程（多进程版本）

        流程：
        1. 模型分发：将全局模型发送给客户端
        2. 多进程本地训练：客户端基于资源分配和网络约束进行多任务训练
        3. 模型上传：收集训练后的模型参数
        """
        self.logger.info("Starting federated client training (multiprocessing)...")
        self.logger.info(
            f"Training {len(clients)} clients with allocated resources and network constraints"
        )

        # Step 1: 准备客户端数据用于多进程
        self.logger.info("Step 1: Preparing client data for multiprocessing...")
        client_data_list = self._prepare_client_data_for_multiprocessing(clients)

        # Step 2: 使用多进程池执行并行训练
        self.logger.info(
            "Step 2: Running parallel local training using multiprocessing..."
        )

        # 确定进程数（不超过客户端数量和CPU核心数）
        import multiprocessing

        num_processes = min(
            len(clients), multiprocessing.cpu_count(), 8
        )  # 限制最大进程数
        self.logger.info(f"Using {num_processes} processes for {len(clients)} clients")

        training_start_time = time.time()

        try:
            # 使用多进程池执行训练
            with multiprocessing.Pool(processes=num_processes) as pool:
                reports = pool.map(train_client_worker, client_data_list)

            training_duration = time.time() - training_start_time

            # Step 3: 处理训练结果
            self.logger.info("Step 3: Processing training results...")
            processed_reports = self._process_multiprocessing_results(reports, clients)

            # 统计成功的客户端
            successful_clients = len(
                [r for r in processed_reports if r.get("success", False)]
            )
            self.logger.info(
                f"Multiprocessing training completed in {training_duration:.2f}s: "
                f"{successful_clients}/{len(clients)} clients successful"
            )

            return processed_reports

        except Exception as e:
            self.logger.error(f"Multiprocessing training failed: {e}")
            # 降级到串行训练
            self.logger.info("Falling back to sequential training...")
            raise

    def _prepare_client_data_for_multiprocessing(
        self, clients: List[MultiTaskClient]
    ) -> List[Dict[str, Any]]:
        """准备客户端数据用于多进程训练"""
        client_data_list = []

        # 获取全局模型权重
        global_model_weights = getattr(self, "global_model_weights", None)

        for client in clients:
            try:
                # 序列化客户端状态
                client_state = client.to_state()

                client_data = {
                    "client_id": client.client_id,
                    "client_state": client_state,
                    "global_model_weights": (
                        copy.deepcopy(global_model_weights)
                        if global_model_weights
                        else None
                    ),
                    "training_config": {
                        "round_num": getattr(self, "current_round", 0),
                        "server_config": {
                            "similarity_threshold": getattr(
                                self.mt_config, "similarity_threshold", 0.5
                            ),
                        },
                    },
                }

                client_data_list.append(client_data)
                self.logger.debug(f"Prepared data for client {client.client_id}")

            except Exception as e:
                self.logger.error(
                    f"Failed to prepare data for client {client.client_id}: {e}"
                )
                raise

        return client_data_list

    def _process_multiprocessing_results(
        self, reports: List[Dict[str, Any]], clients: List[MultiTaskClient]
    ) -> List[Any]:
        """处理多进程训练结果"""
        processed_reports = []

        for i, report in enumerate(reports):
            try:
                if report.get("success", False):
                    # 成功的训练报告
                    client = clients[i] if i < len(clients) else None

                    # 更新客户端的模型权重（如果需要）
                    if client and "model_weights" in report:
                        # 这里可以选择是否更新本地客户端的权重
                        pass

                    # 创建标准化的训练报告
                    processed_report = self._create_standardized_report(report, client)
                    processed_reports.append(processed_report)

                    self.logger.info(
                        f"Client {report['client_id']}: Training completed in {report['training_duration']:.2f}s"
                    )
                    self.logger.info(
                        f"  Tasks trained: {list(report['training_metrics'].keys())}"
                    )

                    # 记录资源利用率
                    resource_util = report.get("resource_utilization", {})
                    if resource_util:
                        self.logger.info(
                            f"  Resource utilization: Compute={resource_util.get('total_compute', 0):.2f}, "
                            f"Power={resource_util.get('total_power', 0):.2f}"
                        )
                else:
                    # 失败的训练报告
                    self.logger.error(
                        f"Client {report['client_id']}: Training failed - {report.get('error', 'Unknown error')}"
                    )
                    processed_reports.append(report)
                    raise ValueError(report.get("error", "Unknown in report error"))

            except Exception as e:
                self.logger.error(f"Failed to process report for client {i}: {e}")
                # 创建一个错误报告
                # error_report = {
                #     "client_id": (
                #         clients[i].client_id if i < len(clients) else f"unknown_{i}"
                #     ),
                #     "success": False,
                #     "error": str(e),
                #     "training_metrics": {},
                #     "training_duration": 0.0,
                #     "model_weights": {},
                # }
                # processed_reports.append(error_report)
                raise

        return processed_reports

    def _run_client_training_sequential(
        self, clients: List[MultiTaskClient]
    ) -> List[Any]:
        """串行客户端训练（降级方案）"""
        self.logger.info("Running sequential client training...")
        reports = []

        # 分发全局模型
        self._distribute_global_model(clients)

        # 串行训练每个客户端
        for client in clients:
            self.logger.info(
                f"Client {client.client_id}: Starting sequential training..."
            )
            training_start_time = time.time()

            try:
                # 执行多任务本地训练
                local_training_metrics = self._execute_local_multi_task_training(client)
                training_duration = time.time() - training_start_time

                # 创建训练报告
                report = self._create_training_report(
                    client, local_training_metrics, training_duration
                )
                reports.append(report)

                self.logger.info(
                    f"Client {client.client_id}: Sequential training completed in {training_duration:.2f}s"
                )

            except Exception as e:
                self.logger.error(
                    f"Client {client.client_id}: Sequential training failed: {e}"
                )
                # 创建错误报告
                error_report = {
                    "client_id": client.client_id,
                    "success": False,
                    "error": str(e),
                    "training_metrics": {},
                    "training_duration": 0.0,
                    "model_weights": {},
                }
                reports.append(error_report)

        return reports

    def _create_standardized_report(
        self, report: Dict[str, Any], client: MultiTaskClient = None
    ):
        """创建标准化的训练报告"""
        try:
            # 创建与原有格式兼容的报告对象
            class TrainingReport:
                def __init__(self, report_dict):
                    self.client_id = report_dict.get("client_id")
                    self.success = report_dict.get("success", False)
                    self.training_metrics = report_dict.get("training_metrics", {})
                    self.training_duration = report_dict.get("training_duration", 0.0)
                    self.model_weights = report_dict.get("model_weights", {})
                    self.resource_utilization = report_dict.get(
                        "resource_utilization", {}
                    )
                    self.error = report_dict.get("error", None)

                    # 计算平均指标（为了兼容性）
                    if self.training_metrics:
                        total_loss = 0.0
                        total_accuracy = 0.0
                        num_tasks = len(self.training_metrics)

                        for task_metrics in self.training_metrics.values():
                            if isinstance(task_metrics, dict):
                                total_loss += task_metrics.get("loss", 0.0)
                                total_accuracy += task_metrics.get("accuracy", 0.0)

                        self.avg_loss = total_loss / max(1, num_tasks)
                        self.avg_accuracy = total_accuracy / max(1, num_tasks)
                    else:
                        self.avg_loss = 0.0
                        self.avg_accuracy = 0.0

            return TrainingReport(report)

        except Exception as e:
            self.logger.error(f"Failed to create standardized report: {e}")
            raise
            # 返回一个基本的错误报告
            # class ErrorReport:
            #     def __init__(self):
            #         self.client_id = report.get("client_id", "unknown")
            #         self.success = False
            #         self.error = str(e)
            #         self.training_metrics = {}
            #         self.training_duration = 0.0
            #         self.model_weights = {}
            #         self.avg_loss = 0.0
            #         self.avg_accuracy = 0.0

            # return ErrorReport()

    def _distribute_global_model(self, clients: List[MultiTaskClient]):
        """将全局模型分发给客户端"""
        self.logger.info("Distributing global model to clients...")

        for client in clients:
            try:
                # 模拟模型分发过程
                if hasattr(self, "global_model_weights") and self.global_model_weights:
                    # 将全局模型权重发送给客户端
                    client.receive_global_model(self.global_model_weights)
                    self.logger.debug(
                        f"Global model distributed to client {client.client_id}"
                    )
                else:
                    # 第一轮，客户端使用初始模型
                    self.logger.debug(
                        f"Client {client.client_id} using initial model (first round)"
                    )

            except Exception as e:
                self.logger.error(
                    f"Failed to distribute model to client {client.client_id}: {e}"
                )

    def _execute_local_multi_task_training(
        self, client: MultiTaskClient
    ) -> Dict[str, Dict[str, float]]:
        """执行客户端的多任务本地训练"""
        # 基于任务相似性和资源分配执行训练
        training_metrics = {}

        # 确保任务相似性已计算
        # if not client.similarity_computed:
        # client.analyze_task_similarity()

        # 根据资源分配调整训练参数
        if client.current_allocation:
            # 基于计算资源分配调整批次大小和学习率
            total_compute = sum(client.current_allocation.q_ij.values())
            batch_size_factor = min(2.0, max(0.5, total_compute))
            learning_rate_factor = min(1.5, max(0.5, total_compute))

            self.logger.debug(
                f"Client {client.client_id}: Adjusted training params based on compute allocation"
            )
            self.logger.debug(
                f"  Total Compute: {total_compute:.2f}, Batch factor: {batch_size_factor:.2f}"
            )

        # 执行实际的多任务训练
        # 调用客户端的多任务训练方法
        training_metrics = client.run()

        # 添加资源利用率信息
        if client.current_allocation:
            for task_name in training_metrics:
                if isinstance(training_metrics[task_name], dict):
                    training_metrics[task_name]["compute_utilization"] = (
                        client.current_allocation.q_ij.get(task_name, 0.0)
                    )
                    training_metrics[task_name]["power_allocation"] = (
                        client.current_allocation.P_ij.get(task_name, 0.0)
                    )
                    training_metrics[task_name]["selection_probability"] = (
                        client.current_allocation.select_ij.get(task_name, 0.0)
                    )
                    training_metrics[task_name]["task_priority"] = (
                        client.current_allocation.Task_ij.get(task_name, 0.0)
                    )

        return training_metrics

    def _create_training_report(
        self,
        client: MultiTaskClient,
        training_metrics: Dict[str, Dict[str, float]],
        training_duration: float,
    ):
        """创建训练报告"""
        # 计算平均性能指标
        total_loss = 0.0
        total_accuracy = 0.0
        num_tasks = len(training_metrics)

        for task_metrics in training_metrics.values():
            if isinstance(task_metrics, dict):
                total_loss += task_metrics.get("loss", 0.0)
                total_accuracy += task_metrics.get("accuracy", 0.0)

        avg_loss = total_loss / max(1, num_tasks)
        avg_accuracy = total_accuracy / max(1, num_tasks)

        # 获取模型权重（简化）
        model_weights = None
        try:
            model_weights = client.get_model_weights()
        except:
            model_weights = {}

        # 计算网络延迟
        network_delay = 0.0
        if client.network_metrics:
            network_delay = sum(metric.delay for metric in client.network_metrics)

        # 创建报告对象（兼容原有FL框架的Report格式）
        report = type(
            "TrainingReport",
            (),
            {
                "client_id": client.client_id,
                "success": True,
                "loss": avg_loss,
                "accuracy": avg_accuracy,
                "training_time": training_duration,
                "model_weights": model_weights,
                "task_metrics": training_metrics,
                "num_tasks": num_tasks,
                "shared_tasks": len(client.shared_tasks),
                "independent_tasks": len(client.independent_tasks),
                "resource_allocation": client.current_allocation,
                "network_metrics": client.network_metrics,
                "delay": network_delay,  # 添加delay属性以兼容原有框架
                "weights": [
                    (f"task_{i}", model_weights)
                    for i, model_weights in enumerate([model_weights])
                ],  # 兼容原有格式
            },
        )()

        return report

    def _get_cpu_utilization(self, client: MultiTaskClient) -> float:
        """获取客户端计算资源利用率"""
        if client.current_allocation and client.current_allocation.q_ij:
            return sum(client.current_allocation.q_ij.values())
        return 0.0

    def _aggregate_models(self, reports: List[Any]) -> Dict[str, Any]:
        """
        聚合客户端模型权重 - 真正的联邦学习聚合过程

        使用FedAvg算法聚合多任务模型，考虑任务相似性
        """
        if not reports:
            self.logger.warning("No training reports to aggregate")
            return {}

        # 过滤成功的训练报告
        successful_reports = [
            r
            for r in reports
            if hasattr(r, "success")
            and r.success
            and hasattr(r, "model_weights")
            and r.model_weights
        ]

        if not successful_reports:
            self.logger.warning("No successful training reports with model weights")
            return {}

        self.logger.info(
            f"Aggregating models from {len(successful_reports)} successful clients"
        )

        # 收集所有任务的权重
        task_weights_collection = {}

        for report in successful_reports:
            client_id = report.client_id

            # 收集每个任务的权重
            for task_name, task_weights in report.model_weights.items():
                if task_name not in task_weights_collection:
                    task_weights_collection[task_name] = []

                task_weights_collection[task_name].append(
                    {
                        "client_id": client_id,
                        "weights": task_weights,
                    }
                )

        aggregated_weights = {}
        client_task_data_size = self._compute_client_task_data_size()
        for task_name, task_data in task_weights_collection.items():
            self.logger.info(
                f"Aggregating task '{task_name}' from {len(task_data)} clients"
            )

            if not task_data:
                continue

            # 聚合单个任务的权重
            aggregated_task_weights = self._fedavg_aggregate_task_weights(
                task_data, task_name, client_task_data_size
            )
            aggregated_weights[task_name] = aggregated_task_weights

        # 保存全局模型权重
        self.global_model_weights = aggregated_weights

        # 记录聚合统计信息
        self.logger.info(f"Model aggregation completed:")
        self.logger.info(f"  Tasks aggregated: {len(aggregated_weights)}")
        self.logger.info(f"  Participating clients: {len(successful_reports)}")

        return aggregated_weights

    def _compute_client_task_data_size(self) -> Dict[int, Dict[str, int]]:
        """计算每个客户端每个任务的数据量"""
        client_task_data_size = {}
        for client in self.clients:
            # 初始化客户端字典
            if client.client_id not in client_task_data_size:
                client_task_data_size[client.client_id] = {}

            for task_name in client.task_data_loaders:
                train_loader, _ = client.task_data_loaders[task_name]
                if train_loader is not None:
                    client_task_data_size[client.client_id][task_name] = len(
                        train_loader.dataset
                    )
        return client_task_data_size

    def _fedavg_aggregate_task_weights(
        self,
        task_data: List[Dict],
        task_name: str,
        client_task_data_size: Dict[int, Dict[str, int]],
    ) -> Dict:
        """聚合单个任务的权重"""
        state_dict_new = {}
        total_samples = 0
        for data in task_data:
            client_id = data["client_id"]
            total_samples += client_task_data_size[client_id][task_name]

        for i, data in enumerate(task_data):
            client_id = data["client_id"]
            weight = client_task_data_size[client_id][task_name] / total_samples
            if i == 0:
                state_dict_new = {
                    key: data["weights"][key] * weight for key in data["weights"]
                }
            else:
                for key in state_dict_new:
                    state_dict_new[key] += data["weights"][key] * weight

        return state_dict_new

    def _calculate_maddpg_rewards(
        self,
        available_clients: List[MultiTaskClient],
        selected_clients: List[MultiTaskClient],
        reports: List[Any],
        network_metrics: Dict[int, List[NetworkMetrics]],
    ) -> Dict[int, float]:
        """Calculate rewards for MADDPG based on training performance, network efficiency, and resource utilization"""
        if self.last_state is None:
            raise ValueError("Last state is None, cannot calculate rewards")
        # reports network_metrics都只有SelectedClients的数据
        # TODO 计算奖励函数
        rewards = {}
        s_id = set()
        alpha1, alpha2 = 1.0, -0.1
        lambda1 = 0.2
        p_beta1 = -0.2
        for idx, client in enumerate(selected_clients):
            s_id.add(client.client_id)
            i = client.client_id
            # r1
            acc_old = self.last_state[client.client_id][6]
            acc_new = reports[idx].accuracy
            accuracy_change = abs(acc_new - acc_old)

            sorted_tasks = sorted(
                client.current_allocation.Task_ij.items(),
                key=lambda item: item[1],
                reverse=True,
            )
            delays = []
            power_sla_rates = []
            energy_uses = []
            for task_name, _ in sorted_tasks:
                if (
                    client.current_allocation.select_ij[task_name]
                    >= self.config.maddpg_config.action_threshold
                ):
                    j = int(task_name[5:])
                    power = client.current_allocation.P_ij[task_name]
                    power = (
                        power + p_beta1 * accuracy_change
                    )  # 为什么要重算一遍，因为这个公式

                    power_sla_rate = calculate_sla_violation_rate(
                        y_pred=np.array([power]),
                        alpha=self.min_power,
                        beta=self.max_power,
                    )
                    power_sla_rates.append(power_sla_rate)

                    power = np.clip(
                        power,
                        self.min_power,
                        self.max_power,
                    )

                    # SNR = P * H / (B * N0 * F)
                    snr = (
                        power * self.channel_gain[self.current_round, i, j]
                    ) / self.noise
                    # Shannon capacity formula (单位是比特/秒)
                    up_rate = self.bandwidth * np.log2(1 + snr)

                    # File size (单位是字节)
                    file_size = self._calculate_file_size(task_id=j, client_id=i)

                    ##### time
                    # T = S/r
                    time_up = file_size * 8 / up_rate

                    # 计算时间应该基于实际训练数据量，而不是模型文件大小
                    data_samples = self._get_client_training_samples(client, task_name)
                    time_cmp = (
                        self.number_of_local_epochs
                        * self.cpu_cycles
                        * data_samples
                        / self.cpu_freq[i]
                    )

                    ##### energy
                    # E = P * T
                    energy_up = power * time_up
                    # 计算能耗也应该基于实际训练数据量
                    energy_cmp = (
                        self.number_of_local_epochs
                        * self.capacitance_coefficient
                        * self.cpu_cycles
                        * data_samples
                        * (self.cpu_freq[i] ** 2)
                    )
                    energy_uses.append(energy_up + energy_cmp)

                    # 顺序执行，后面的任务需要等待前面的任务完成，所以需要加上前面的任务延迟
                    length = len(network_metrics[i])
                    last_task_delay = (
                        network_metrics[i][length - 1].delay if length > 0 else 0
                    )
                    delay = time_up + time_cmp + last_task_delay
                    delays.append(delay)
                    # 如果delay 数值变成了无穷大，那抛出错误
                    if delay == float("inf"):
                        raise ValueError(
                            f"Delay is inf for client {i}, task {task_name}"
                        )

            avg_delay = np.mean(delays)
            r1 = -(accuracy_change + lambda1 * avg_delay)

            # r2
            gradient_magnitude = np.mean(
                [
                    np.sqrt(
                        sum(
                            np.linalg.norm(
                                reports[idx]
                                .model_weights[task_name][param_name]
                                .cpu()
                                .numpy()
                                - self.global_model_weights[task_name][param_name]
                                .cpu()
                                .numpy()
                            )
                            ** 2
                            for param_name in reports[idx].model_weights[task_name]
                            if param_name in self.global_model_weights[task_name]
                        )
                    )
                    for task_name in reports[idx].model_weights
                    if task_name in self.global_model_weights
                ]
            )
            r2 = -(
                gradient_magnitude
                + lambda1
                * np.log2(
                    1
                    + np.mean(
                        [
                            metric.ph_divideby_n0
                            for metric in network_metrics[client.client_id]
                        ]
                    )
                )
            )

            # r6
            avg_energy = np.mean(energy_uses)
            r6 = acc_new / avg_energy
            # r7
            r7 = acc_new / avg_delay
            avg_power_sla_rate = np.mean(power_sla_rates)

            rewards[client.client_id] = (
                alpha1 * r1 + alpha2 * r2 + r6 + r7 - avg_power_sla_rate
            )

        for client in available_clients:
            if client.client_id not in s_id:
                rewards[client.client_id] = 0.0

        return rewards

    def _collect_round_metrics(
        self,
        round_num: int,
        clients: List[MultiTaskClient],
        reports: List[Any],
        network_metrics: Dict[int, List[NetworkMetrics]],
        rewards: Dict[int, float],
    ) -> Dict[str, Any]:
        """Collect comprehensive metrics for the round 这里计算的平均值到后面都不会用到，在修改代码的时候可以起到一个辅助作用"""

        # Training metrics
        avg_loss = np.mean([report.loss for report in reports]) if reports else 0.0
        avg_accuracy = (
            np.mean([report.accuracy for report in reports]) if reports else 0.0
        )
        avg_delay = np.mean([report.delay for report in reports]) if reports else 0.0

        # Network metrics
        avg_energy = (
            np.mean(
                [
                    energy_use
                    for nm in network_metrics.values()
                    for energy_use in (metric.energy_use for metric in nm)
                ]
            )
            if network_metrics
            else 0.0
        )
        avg_round_time = (
            np.mean(
                [
                    delay
                    for nm in network_metrics.values()
                    for delay in (metric.delay for metric in nm)
                ]
            )
            if network_metrics
            else 0.0
        )

        # MADDPG metrics
        avg_reward = np.mean(list(rewards.values())) if rewards else 0.0
        maddpg_stats = self.maddpg_allocator.get_training_stats()

        # Task similarity metrics
        shared_task_count = sum(len(client.shared_tasks) for client in clients)
        independent_task_count = sum(
            len(client.independent_tasks) for client in clients
        )

        return {
            "round": round_num,
            "timestamp": time.time(),
            "num_clients": len(clients),
            "training_metrics": {
                "avg_loss": avg_loss,
                "avg_accuracy": avg_accuracy,
                "avg_delay": avg_delay,
            },
            "network_metrics": {
                "avg_energy": avg_energy,
                "avg_round_time": avg_round_time,
            },
            "maddpg_metrics": {
                "avg_reward": avg_reward,
                "replay_buffer_size": maddpg_stats["replay_buffer_size"],
            },
            "task_similarity_metrics": {
                "shared_task_groups": shared_task_count,
                "independent_tasks": independent_task_count,
            },
        }

    def reset(self, ep: int):
        # TODO reset
        if ep != 0:  # ep=0时，make_clients()已经在创建类的时候调用过了
            self.clients = []
            self.make_clients(self.config.clients.total)

        self.round_metrics = []

        self.current_round = 0

        self.state = np.zeros(
            (self.config.clients.total, self.maddpg_allocator.state_dim)
        )
        self.last_state = self.state
        return self.state

    def run_experiment(self):
        self.logger.info("Starting multi-task federated learning experiment")

        # Initialize model path (for compatibility)
        model_path = self.config.paths.model
        sys.path.append(model_path)

        best_accuracy = 0.0

        self.logger.info(f"Total episodes: {self.config.maddpg_config.episodes}")

        for ep in range(self.config.maddpg_config.episodes):
            states = self.reset(ep)
            episode_rewards = 0.0
            self.logger.info("=" * 10 + f"Episode {ep+1} started" + "=" * 10)
            # Run federated rounds
            for round_num in range(self.mt_config.num_rounds):
                self.current_round = round_num

                try:
                    round_metrics, states = self.run_federated_round(round_num, states)
                    avg_reward = round_metrics["maddpg_metrics"]["avg_reward"]
                    episode_rewards += avg_reward
                    # Log progress
                    if round_num % 10 == 0:
                        self._log_progress(ep + 1, round_num, round_metrics)

                    if self.tensorboard:
                        self.writer.add_scalar(
                            f"Rewards/ep_{ep+1}", avg_reward, round_num + 1
                        )

                except Exception as e:
                    self.logger.error(f"Ep {ep+1}, Round {round_num} failed: {e}")
                    raise

            # Final results
            self._generate_final_report(ep + 1)
            self.logger.info(
                "=" * 10
                + f"Episode {ep+1} completed, total reward: {episode_rewards}"
                + "=" * 10
            )

            if self.tensorboard:
                self.writer.add_scalar("TotalRewards", episode_rewards, ep + 1)
                self.writer.flush()

            self.episode_metrics[ep] = self.round_metrics

            final_accuracy = self.round_metrics[-1]["training_metrics"]["avg_accuracy"]
            if best_accuracy < final_accuracy:
                best_accuracy = final_accuracy
                if not os.path.exists(self.config.maddpg_config.chkpt_dir):
                    os.makedirs(self.config.maddpg_config.chkpt_dir)
                self.maddpg_allocator.save_checkpoint(
                    self.config.maddpg_config.chkpt_dir
                )

        self.logger.info("Multi-task federated learning experiment completed")

    def _log_progress(self, ep: int, round_num: int, metrics: Dict[str, Any]):
        """Log training progress"""
        training = metrics["training_metrics"]
        network = metrics["network_metrics"]
        maddpg = metrics["maddpg_metrics"]

        self.logger.info(f"ep {ep}, Round {round_num} Progress:")
        self.logger.info(
            f"  Training - Loss: {training['avg_loss']:.4f}, Accuracy: {training['avg_accuracy']:.4f}, Delay: {training['avg_delay']:.2f}s"
        )
        self.logger.info(
            f"  Network - Energy: {network['avg_energy']}, Delay: {network['avg_round_time']:.2f}s"
        )
        self.logger.info(
            f"  MADDPG - Avg Reward: {maddpg['avg_reward']:.3f}, Buffer Size: {maddpg['replay_buffer_size']}"
        )

    def _generate_final_report(self, ep: int):
        """Generate final experiment report  这里计算的平均值到后面都不会用到，在修改代码的时候可以起到一个辅助作用"""
        if not self.round_metrics:
            self.logger.warning(f"Ep {ep}, No round metrics available for final report")
            return

        # Calculate final statistics
        final_loss = self.round_metrics[-1]["training_metrics"]["avg_loss"]
        initial_loss = self.round_metrics[0]["training_metrics"]["avg_loss"]
        loss_improvement = initial_loss - final_loss

        final_accuracy = self.round_metrics[-1]["training_metrics"]["avg_accuracy"]
        initial_accuracy = self.round_metrics[0]["training_metrics"]["avg_accuracy"]
        accuracy_improvement = final_accuracy - initial_accuracy

        avg_energy = np.mean(
            [rm["network_metrics"]["avg_energy"] for rm in self.round_metrics]
        )
        avg_reward = np.mean(
            [rm["maddpg_metrics"]["avg_reward"] for rm in self.round_metrics]
        )

        self.logger.info("=" * 60)
        self.logger.info(f"EP {ep} , MULTI-TASK FEDERATED LEARNING FINAL REPORT")
        self.logger.info("=" * 60)
        self.logger.info(f"Total Rounds: {len(self.round_metrics)}")
        self.logger.info(
            f"Loss Improvement: {loss_improvement:.4f} ({initial_loss:.4f} → {final_loss:.4f})"
        )
        self.logger.info(
            f"Accuracy Improvement: {accuracy_improvement:.4f} ({initial_accuracy:.4f} → {final_accuracy:.4f})"
        )
        self.logger.info(f"Average Network Energy: {avg_energy}")
        self.logger.info(f"Average MADDPG Reward: {avg_reward:.3f}")
        self.logger.info(
            f"MADDPG Buffer Size: {self.maddpg_allocator.get_training_stats()['replay_buffer_size']}"
        )
        self.logger.info("=" * 60)
