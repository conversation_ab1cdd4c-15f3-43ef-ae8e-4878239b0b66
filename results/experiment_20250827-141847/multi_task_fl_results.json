{"experiment_name": "multi_task_fl", "config": {"episodes": 2, "state_dim": 21, "action_dim": 12, "num_clients": 5, "num_rounds": 50, "clients_per_round": 3, "tasks": ["mnist", "cifar10", "airquality"], "similarity_threshold": 0.5, "network_type": "wifi"}, "duration": 188.17892265319824, "epsode_metrics": {"0": [{"round": 0, "timestamp": 1756275548.952241, "num_clients": 2, "training_metrics": {"avg_loss": 1.5032470029157898, "avg_accuracy": 0.507276205252436, "avg_delay": 114016.66030152561}, "network_metrics": {"avg_energy": 64689.49541275523, "avg_round_time": 114016.66030152561}, "maddpg_metrics": {"avg_reward": 0.24424778597268001, "replay_buffer_size": 1}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 6}}, {"round": 1, "timestamp": 1756275551.7457914, "num_clients": 4, "training_metrics": {"avg_loss": 1.0323182198529441, "avg_accuracy": 0.6534598218230199, "avg_delay": 446401.62028000946}, "network_metrics": {"avg_energy": 139944.4140648863, "avg_round_time": 297601.080186673}, "maddpg_metrics": {"avg_reward": 0.41899120015186925, "replay_buffer_size": 2}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 12}}, {"round": 2, "timestamp": 1756275553.887678, "num_clients": 3, "training_metrics": {"avg_loss": 0.8477985519501897, "avg_accuracy": 0.714928977956094, "avg_delay": 4270775.580596409}, "network_metrics": {"avg_energy": 1143652.9525229791, "avg_round_time": 3203081.685447307}, "maddpg_metrics": {"avg_reward": 0.4893013717698489, "replay_buffer_size": 3}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 9}}, {"round": 3, "timestamp": 1756275556.7577813, "num_clients": 4, "training_metrics": {"avg_loss": 0.621563329633015, "avg_accuracy": 0.7435402779927537, "avg_delay": 1798921.0043681264}, "network_metrics": {"avg_energy": 660668.006812211, "avg_round_time": 1027954.8596389294}, "maddpg_metrics": {"avg_reward": 0.6303222364454246, "replay_buffer_size": 4}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 12}}, {"round": 4, "timestamp": 1756275560.2395687, "num_clients": 5, "training_metrics": {"avg_loss": 0.5723424191703088, "avg_accuracy": 0.7631533215724753, "avg_delay": 1944604.139376896}, "network_metrics": {"avg_energy": 524218.70263278205, "avg_round_time": 1080335.6329871644}, "maddpg_metrics": {"avg_reward": 0.6147473641301306, "replay_buffer_size": 5}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 5, "timestamp": 1756275563.0710976, "num_clients": 4, "training_metrics": {"avg_loss": 0.48580590368752985, "avg_accuracy": 0.7564554471951258, "avg_delay": 2542248.051125603}, "network_metrics": {"avg_energy": 909793.6406987674, "avg_round_time": 1694832.0340837354}, "maddpg_metrics": {"avg_reward": 0.6869925987125007, "replay_buffer_size": 6}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 12}}, {"round": 6, "timestamp": 1756275566.502037, "num_clients": 5, "training_metrics": {"avg_loss": 0.4997752517694608, "avg_accuracy": 0.7686059767444722, "avg_delay": 1813340.6023454268}, "network_metrics": {"avg_energy": 563867.2752842391, "avg_round_time": 906670.3011727134}, "maddpg_metrics": {"avg_reward": 0.671213668433718, "replay_buffer_size": 7}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 7, "timestamp": 1756275569.9212964, "num_clients": 5, "training_metrics": {"avg_loss": 0.4824776838572385, "avg_accuracy": 0.7638537058003928, "avg_delay": 1513524.880685116}, "network_metrics": {"avg_energy": 615303.3710836098, "avg_round_time": 1081089.2004893685}, "maddpg_metrics": {"avg_reward": 0.6698371016190571, "replay_buffer_size": 8}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 8, "timestamp": 1756275573.3743696, "num_clients": 5, "training_metrics": {"avg_loss": 0.4709491909908441, "avg_accuracy": 0.7699268866511975, "avg_delay": 2270027.4537942884}, "network_metrics": {"avg_energy": 590327.0328484636, "avg_round_time": 1418767.1586214304}, "maddpg_metrics": {"avg_reward": 0.6788723419433451, "replay_buffer_size": 9}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 9, "timestamp": 1756275576.8051012, "num_clients": 5, "training_metrics": {"avg_loss": 0.46419685020421947, "avg_accuracy": 0.7735488405517226, "avg_delay": 2201422.7153345766}, "network_metrics": {"avg_energy": 763173.8447668036, "avg_round_time": 1834518.9294454802}, "maddpg_metrics": {"avg_reward": 0.6878922779098469, "replay_buffer_size": 10}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 10, "timestamp": 1756275580.2726107, "num_clients": 5, "training_metrics": {"avg_loss": 0.46454491358405603, "avg_accuracy": 0.7717783854776044, "avg_delay": 2341391.8077379037}, "network_metrics": {"avg_energy": 532287.085557691, "avg_round_time": 1463369.8798361896}, "maddpg_metrics": {"avg_reward": 0.6723825672354199, "replay_buffer_size": 11}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 11, "timestamp": 1756275583.7287216, "num_clients": 5, "training_metrics": {"avg_loss": 0.46090125912451185, "avg_accuracy": 0.7685152306534749, "avg_delay": 2159280.756344388}, "network_metrics": {"avg_energy": 513384.9616393097, "avg_round_time": 1199600.4201913262}, "maddpg_metrics": {"avg_reward": 0.6692971204175572, "replay_buffer_size": 12}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 12, "timestamp": 1756275587.170247, "num_clients": 5, "training_metrics": {"avg_loss": 0.4574268697799805, "avg_accuracy": 0.7700583611240508, "avg_delay": 1902887.9825706154}, "network_metrics": {"avg_energy": 622254.8100997346, "avg_round_time": 1359205.7018361539}, "maddpg_metrics": {"avg_reward": 0.6918504950089884, "replay_buffer_size": 13}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 13, "timestamp": 1756275590.635413, "num_clients": 5, "training_metrics": {"avg_loss": 0.4579295939998701, "avg_accuracy": 0.7709910896576561, "avg_delay": 186093.00320570957}, "network_metrics": {"avg_energy": 127699.10438660737, "avg_round_time": 155077.50267142462}, "maddpg_metrics": {"avg_reward": 0.6821273741226532, "replay_buffer_size": 14}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 14, "timestamp": 1756275594.1033154, "num_clients": 5, "training_metrics": {"avg_loss": 0.45739817959644513, "avg_accuracy": 0.7738036034641614, "avg_delay": 1203139.6694952375}, "network_metrics": {"avg_energy": 590251.1878637171, "avg_round_time": 668410.9274973541}, "maddpg_metrics": {"avg_reward": 0.617040728161422, "replay_buffer_size": 15}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 15, "timestamp": 1756275597.5711565, "num_clients": 5, "training_metrics": {"avg_loss": 0.45789048336640314, "avg_accuracy": 0.7716775615913475, "avg_delay": 1395042.1152612437}, "network_metrics": {"avg_energy": 650936.6637796357, "avg_round_time": 871901.3220382773}, "maddpg_metrics": {"avg_reward": 0.6588764217899398, "replay_buffer_size": 16}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 16, "timestamp": 1756275601.0634294, "num_clients": 5, "training_metrics": {"avg_loss": 0.46589674910840895, "avg_accuracy": 0.7694825818186894, "avg_delay": 1811965.8341795139}, "network_metrics": {"avg_energy": 536404.8123209171, "avg_round_time": 905982.9170897568}, "maddpg_metrics": {"avg_reward": 0.6136413003566364, "replay_buffer_size": 17}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 17, "timestamp": 1756275604.5454838, "num_clients": 5, "training_metrics": {"avg_loss": 0.4675384523269409, "avg_accuracy": 0.7735594003417081, "avg_delay": 1430193.2749208263}, "network_metrics": {"avg_energy": 670697.945982514, "avg_round_time": 893870.7968255165}, "maddpg_metrics": {"avg_reward": 0.6526849916157607, "replay_buffer_size": 18}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 18, "timestamp": 1756275608.0378406, "num_clients": 5, "training_metrics": {"avg_loss": 0.47215646339075956, "avg_accuracy": 0.7705500024456529, "avg_delay": 1616948.6619349068}, "network_metrics": {"avg_energy": 621454.4182785754, "avg_round_time": 898304.8121860593}, "maddpg_metrics": {"avg_reward": 0.6509555787099445, "replay_buffer_size": 19}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 19, "timestamp": 1756275611.498628, "num_clients": 5, "training_metrics": {"avg_loss": 0.46171532401155363, "avg_accuracy": 0.7698456987852956, "avg_delay": 1385158.7474675546}, "network_metrics": {"avg_energy": 596066.9150746037, "avg_round_time": 769532.6374819749}, "maddpg_metrics": {"avg_reward": 0.6153138703953787, "replay_buffer_size": 20}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 20, "timestamp": 1756275614.9582305, "num_clients": 5, "training_metrics": {"avg_loss": 0.4692669397724482, "avg_accuracy": 0.7771521570261701, "avg_delay": 1288606.101473027}, "network_metrics": {"avg_energy": 605157.2721100473, "avg_round_time": 715892.278596126}, "maddpg_metrics": {"avg_reward": 0.6122932240910207, "replay_buffer_size": 21}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 21, "timestamp": 1756275618.4741223, "num_clients": 5, "training_metrics": {"avg_loss": 0.46437364606730025, "avg_accuracy": 0.7739679610800275, "avg_delay": 1333987.9227463182}, "network_metrics": {"avg_energy": 627035.2887303715, "avg_round_time": 741104.4015257324}, "maddpg_metrics": {"avg_reward": 0.6150364494828924, "replay_buffer_size": 22}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 22, "timestamp": 1756275621.9270594, "num_clients": 5, "training_metrics": {"avg_loss": 0.4690674247026133, "avg_accuracy": 0.7739062924878473, "avg_delay": 1532081.807911704}, "network_metrics": {"avg_energy": 614246.7251839328, "avg_round_time": 766040.903955852}, "maddpg_metrics": {"avg_reward": 0.6123730301189546, "replay_buffer_size": 23}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 23, "timestamp": 1756275625.4559042, "num_clients": 5, "training_metrics": {"avg_loss": 0.4691256548947422, "avg_accuracy": 0.771580615813279, "avg_delay": 1301793.7951577706}, "network_metrics": {"avg_energy": 626849.8943407937, "avg_round_time": 723218.7750876504}, "maddpg_metrics": {"avg_reward": 0.6123497380421031, "replay_buffer_size": 24}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 24, "timestamp": 1756275628.9229093, "num_clients": 5, "training_metrics": {"avg_loss": 0.4641429710570567, "avg_accuracy": 0.7756649637858926, "avg_delay": 1533464.612920858}, "network_metrics": {"avg_energy": 637971.8108430096, "avg_round_time": 851924.7849560322}, "maddpg_metrics": {"avg_reward": 0.6196595400768938, "replay_buffer_size": 25}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 25, "timestamp": 1756275632.3947964, "num_clients": 5, "training_metrics": {"avg_loss": 0.4713146876718383, "avg_accuracy": 0.7730075595936945, "avg_delay": 1380561.9035086657}, "network_metrics": {"avg_energy": 698109.2702165048, "avg_round_time": 862851.1896929162}, "maddpg_metrics": {"avg_reward": 0.6499786604676233, "replay_buffer_size": 26}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 26, "timestamp": 1756275635.817543, "num_clients": 5, "training_metrics": {"avg_loss": 0.47106706991811126, "avg_accuracy": 0.7752529664803787, "avg_delay": 1347389.1905501722}, "network_metrics": {"avg_energy": 629015.9978707205, "avg_round_time": 748549.5503056514}, "maddpg_metrics": {"avg_reward": 0.6122064769034655, "replay_buffer_size": 27}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 27, "timestamp": 1756275639.2479053, "num_clients": 5, "training_metrics": {"avg_loss": 0.47476531945867456, "avg_accuracy": 0.7672160219449806, "avg_delay": Infinity}, "network_metrics": {"avg_energy": NaN, "avg_round_time": Infinity}, "maddpg_metrics": {"avg_reward": 0.6100938722165302, "replay_buffer_size": 28}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 15}}, {"round": 28, "timestamp": 1756275641.3572652, "num_clients": 3, "training_metrics": {"avg_loss": 0.44826055293702255, "avg_accuracy": 0.7723431370999188, "avg_delay": 2705642.6674721614}, "network_metrics": {"avg_energy": 1153055.1458196507, "avg_round_time": 1623385.600483297}, "maddpg_metrics": {"avg_reward": 0.622700586836996, "replay_buffer_size": 29}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 9}}, {"round": 29, "timestamp": 1756275644.1389809, "num_clients": 4, "training_metrics": {"avg_loss": 0.42707399249654066, "avg_accuracy": 0.7689145437934927, "avg_delay": 1795514.7441185107}, "network_metrics": {"avg_energy": 827428.5282032778, "avg_round_time": 1026008.4252105776}, "maddpg_metrics": {"avg_reward": 0.634822875504593, "replay_buffer_size": 30}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 12}}, {"round": 30, "timestamp": 1756275646.2348018, "num_clients": 3, "training_metrics": {"avg_loss": 0.3621172162190002, "avg_accuracy": 0.7693451455914243, "avg_delay": 332589.58563168405}, "network_metrics": {"avg_energy": 159728.34408898553, "avg_round_time": 199553.75137901044}, "maddpg_metrics": {"avg_reward": 0.6553860956712922, "replay_buffer_size": 31}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 9}}, {"round": 31, "timestamp": 1756275647.6358633, "num_clients": 2, "training_metrics": {"avg_loss": 0.3369605015468551, "avg_accuracy": 0.7779968549265264, "avg_delay": 452080.9909457355}, "network_metrics": {"avg_energy": 353856.42270141223, "avg_round_time": 452080.9909457355}, "maddpg_metrics": {"avg_reward": 0.6735147222013405, "replay_buffer_size": 32}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 6}}, {"round": 32, "timestamp": 1756275648.365564, "num_clients": 1, "training_metrics": {"avg_loss": 0.01979639928807349, "avg_accuracy": 0.7646399157895827, "avg_delay": 13715.778364915666}, "network_metrics": {"avg_energy": 13715.778364937862, "avg_round_time": 13715.778364915666}, "maddpg_metrics": {"avg_reward": 0.7920814402847707, "replay_buffer_size": 33}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 33, "timestamp": 1756275649.0887475, "num_clients": 1, "training_metrics": {"avg_loss": 0.018358369707129896, "avg_accuracy": 0.7613239881622146, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.882656652117148, "replay_buffer_size": 34}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 34, "timestamp": 1756275649.8107622, "num_clients": 1, "training_metrics": {"avg_loss": 0.01628844945904954, "avg_accuracy": 0.7616882240525537, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8834846202163802, "replay_buffer_size": 35}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 35, "timestamp": 1756275650.5326958, "num_clients": 1, "training_metrics": {"avg_loss": 0.017918641036279343, "avg_accuracy": 0.7665831678138669, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8828325435854883, "replay_buffer_size": 36}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 36, "timestamp": 1756275651.2491145, "num_clients": 1, "training_metrics": {"avg_loss": 0.01650274721517538, "avg_accuracy": 0.7676621244764856, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8833989011139299, "replay_buffer_size": 37}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 37, "timestamp": 1756275651.9572713, "num_clients": 1, "training_metrics": {"avg_loss": 0.014405179223103914, "avg_accuracy": 0.7646549806271082, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8842379283107584, "replay_buffer_size": 38}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 38, "timestamp": 1756275652.6632676, "num_clients": 1, "training_metrics": {"avg_loss": 0.019284650535458542, "avg_accuracy": 0.7700988537240936, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8822861397858166, "replay_buffer_size": 39}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 39, "timestamp": 1756275653.378353, "num_clients": 1, "training_metrics": {"avg_loss": 0.014393954050319735, "avg_accuracy": 0.7722699164038032, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8842424183798722, "replay_buffer_size": 40}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 40, "timestamp": 1756275654.0918498, "num_clients": 1, "training_metrics": {"avg_loss": 0.020591543143382296, "avg_accuracy": 0.7666923531894332, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8817633827426471, "replay_buffer_size": 41}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 41, "timestamp": 1756275654.8208418, "num_clients": 1, "training_metrics": {"avg_loss": 0.015342288361959314, "avg_accuracy": 0.7731700431545172, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8838630846552162, "replay_buffer_size": 42}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 42, "timestamp": 1756275655.543052, "num_clients": 1, "training_metrics": {"avg_loss": 0.013795734703307971, "avg_accuracy": 0.7712385578496725, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8844817061186768, "replay_buffer_size": 43}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 43, "timestamp": 1756275656.3193839, "num_clients": 1, "training_metrics": {"avg_loss": 0.015034284098267866, "avg_accuracy": 0.7699975058252845, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8839862863606929, "replay_buffer_size": 44}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 44, "timestamp": 1756275657.1019135, "num_clients": 1, "training_metrics": {"avg_loss": 0.01794233757876403, "avg_accuracy": 0.7675628711726907, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8828230649684944, "replay_buffer_size": 45}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 45, "timestamp": 1756275657.894513, "num_clients": 1, "training_metrics": {"avg_loss": 0.015837714747855596, "avg_accuracy": 0.7701270722119776, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8836649141008578, "replay_buffer_size": 46}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 46, "timestamp": 1756275658.684274, "num_clients": 1, "training_metrics": {"avg_loss": 0.011638274472109819, "avg_accuracy": 0.7630672675867015, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8853446902111561, "replay_buffer_size": 47}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 47, "timestamp": 1756275659.4816794, "num_clients": 1, "training_metrics": {"avg_loss": 0.012454990800809659, "avg_accuracy": 0.7714952441077413, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8850180036796762, "replay_buffer_size": 48}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 48, "timestamp": 1756275660.2616043, "num_clients": 1, "training_metrics": {"avg_loss": 0.012369555481806552, "avg_accuracy": 0.7612368209624255, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8850521778072774, "replay_buffer_size": 49}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 49, "timestamp": 1756275661.017131, "num_clients": 1, "training_metrics": {"avg_loss": 0.014198972950301444, "avg_accuracy": 0.7650718616710734, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8843204108198794, "replay_buffer_size": 50}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}], "1": [{"round": 0, "timestamp": 1756275680.9052293, "num_clients": 1, "training_metrics": {"avg_loss": 0.017271090055146487, "avg_accuracy": 0.7672517953340144, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8830915639779414, "replay_buffer_size": 51}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 1, "timestamp": 1756275681.6336982, "num_clients": 1, "training_metrics": {"avg_loss": 0.02104740360179373, "avg_accuracy": 0.7715305994859216, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8815810385592825, "replay_buffer_size": 52}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 2, "timestamp": 1756275682.34982, "num_clients": 1, "training_metrics": {"avg_loss": 0.026173386606387794, "avg_accuracy": 0.7715822574656949, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8795306453574449, "replay_buffer_size": 53}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 3, "timestamp": 1756275683.086102, "num_clients": 1, "training_metrics": {"avg_loss": 0.02521460345815285, "avg_accuracy": 0.7384514045415015, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8799141586167388, "replay_buffer_size": 54}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 4, "timestamp": 1756275683.83075, "num_clients": 1, "training_metrics": {"avg_loss": 0.02376270568978119, "avg_accuracy": 0.7546451038165056, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8804949177240876, "replay_buffer_size": 55}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 5, "timestamp": 1756275684.565725, "num_clients": 1, "training_metrics": {"avg_loss": 0.016135821304487763, "avg_accuracy": 0.7629762723939205, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.883545671478205, "replay_buffer_size": 56}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 6, "timestamp": 1756275685.2919204, "num_clients": 1, "training_metrics": {"avg_loss": 0.01496383673050635, "avg_accuracy": 0.7547418106588478, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8840144653077975, "replay_buffer_size": 57}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 7, "timestamp": 1756275686.0121653, "num_clients": 1, "training_metrics": {"avg_loss": 0.013655548584817248, "avg_accuracy": 0.751518903334253, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8845377805660731, "replay_buffer_size": 58}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 8, "timestamp": 1756275686.732477, "num_clients": 1, "training_metrics": {"avg_loss": 0.01586593602769426, "avg_accuracy": 0.752459394882728, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8836536255889224, "replay_buffer_size": 59}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 9, "timestamp": 1756275687.452583, "num_clients": 1, "training_metrics": {"avg_loss": 0.014615415278967703, "avg_accuracy": 0.7531281691120194, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.884153833888413, "replay_buffer_size": 60}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 10, "timestamp": 1756275688.1749156, "num_clients": 1, "training_metrics": {"avg_loss": 0.016651913933552958, "avg_accuracy": 0.7479547209209962, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8833392344265788, "replay_buffer_size": 61}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 11, "timestamp": 1756275688.907137, "num_clients": 1, "training_metrics": {"avg_loss": 0.025392884520745913, "avg_accuracy": 0.7595304186169192, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8798428461917017, "replay_buffer_size": 62}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 12, "timestamp": 1756275689.637051, "num_clients": 1, "training_metrics": {"avg_loss": 0.03440001985776083, "avg_accuracy": 0.7567700754134742, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8762399920568957, "replay_buffer_size": 63}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 13, "timestamp": 1756275690.3520997, "num_clients": 1, "training_metrics": {"avg_loss": 0.03814948815003542, "avg_accuracy": 0.7533342713821646, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8747402047399858, "replay_buffer_size": 64}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 14, "timestamp": 1756275691.0643811, "num_clients": 1, "training_metrics": {"avg_loss": 0.03871055439230986, "avg_accuracy": 0.745378128283552, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.874515778243076, "replay_buffer_size": 65}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 15, "timestamp": 1756275691.776019, "num_clients": 1, "training_metrics": {"avg_loss": 0.03620136730144926, "avg_accuracy": 0.7551475119555139, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8755194530794204, "replay_buffer_size": 66}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 16, "timestamp": 1756275692.4860132, "num_clients": 1, "training_metrics": {"avg_loss": 0.03233820360522562, "avg_accuracy": 0.7677854439502553, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8770647185579098, "replay_buffer_size": 67}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 17, "timestamp": 1756275693.1997678, "num_clients": 1, "training_metrics": {"avg_loss": 0.020804410400160123, "avg_accuracy": 0.7506558879220732, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.881678235839936, "replay_buffer_size": 68}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 18, "timestamp": 1756275693.9095876, "num_clients": 1, "training_metrics": {"avg_loss": 0.01758315466334655, "avg_accuracy": 0.7517172580103343, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8829667381346614, "replay_buffer_size": 69}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 19, "timestamp": 1756275694.619303, "num_clients": 1, "training_metrics": {"avg_loss": 0.014033468225534307, "avg_accuracy": 0.7566174342079351, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8843866127097862, "replay_buffer_size": 70}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 20, "timestamp": 1756275695.3299687, "num_clients": 1, "training_metrics": {"avg_loss": 0.014240083415794894, "avg_accuracy": 0.7547221421068085, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.884303966633682, "replay_buffer_size": 71}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 21, "timestamp": 1756275696.0405233, "num_clients": 1, "training_metrics": {"avg_loss": 0.01042506429378894, "avg_accuracy": 0.7711551178968942, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8858299742824844, "replay_buffer_size": 72}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 22, "timestamp": 1756275696.7543585, "num_clients": 1, "training_metrics": {"avg_loss": 0.01224985556776422, "avg_accuracy": 0.7698741257521265, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8851000577728944, "replay_buffer_size": 73}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 23, "timestamp": 1756275697.4584491, "num_clients": 1, "training_metrics": {"avg_loss": 0.010981115468287802, "avg_accuracy": 0.7696627952275303, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.885607553812685, "replay_buffer_size": 74}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 24, "timestamp": 1756275698.1777484, "num_clients": 1, "training_metrics": {"avg_loss": 0.011666553502436727, "avg_accuracy": 0.773404694769023, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8853333785990253, "replay_buffer_size": 75}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 25, "timestamp": 1756275698.89755, "num_clients": 1, "training_metrics": {"avg_loss": 0.014946726464283225, "avg_accuracy": 0.764611873062786, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8840213094142867, "replay_buffer_size": 76}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 26, "timestamp": 1756275699.6104221, "num_clients": 1, "training_metrics": {"avg_loss": 0.011409719326062865, "avg_accuracy": 0.769777683661044, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8854361122695749, "replay_buffer_size": 77}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 27, "timestamp": 1756275700.350798, "num_clients": 1, "training_metrics": {"avg_loss": 0.015598000235210444, "avg_accuracy": 0.7650205095604267, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8837607999059158, "replay_buffer_size": 78}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 28, "timestamp": 1756275701.0647054, "num_clients": 1, "training_metrics": {"avg_loss": 0.013762293756978275, "avg_accuracy": 0.7650547237181161, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8844950824972087, "replay_buffer_size": 79}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 29, "timestamp": 1756275701.7883916, "num_clients": 1, "training_metrics": {"avg_loss": 0.014562949483358048, "avg_accuracy": 0.7620908629746964, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8841748202066567, "replay_buffer_size": 80}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 30, "timestamp": 1756275702.5036867, "num_clients": 1, "training_metrics": {"avg_loss": 0.01486231692858079, "avg_accuracy": 0.7714961498344763, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8840550732285677, "replay_buffer_size": 81}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 31, "timestamp": 1756275703.212381, "num_clients": 1, "training_metrics": {"avg_loss": 0.011591752219828777, "avg_accuracy": 0.7699209060747227, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8853632991120685, "replay_buffer_size": 82}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 32, "timestamp": 1756275703.9296107, "num_clients": 1, "training_metrics": {"avg_loss": 0.013725549450403681, "avg_accuracy": 0.7667110572459604, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8845097802198386, "replay_buffer_size": 83}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 33, "timestamp": 1756275704.640351, "num_clients": 1, "training_metrics": {"avg_loss": 0.011042399858221566, "avg_accuracy": 0.7665718980284906, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8855830400567114, "replay_buffer_size": 84}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 34, "timestamp": 1756275705.361164, "num_clients": 1, "training_metrics": {"avg_loss": 0.012730324644508073, "avg_accuracy": 0.7660129503824931, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8849078701421967, "replay_buffer_size": 85}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 35, "timestamp": 1756275706.1041772, "num_clients": 1, "training_metrics": {"avg_loss": 0.011945928042526551, "avg_accuracy": 0.7665168505360477, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8852216287829894, "replay_buffer_size": 86}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 36, "timestamp": 1756275706.8200655, "num_clients": 1, "training_metrics": {"avg_loss": 0.013336201136856593, "avg_accuracy": 0.7761600105672994, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8846655195452574, "replay_buffer_size": 87}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 37, "timestamp": 1756275707.5377195, "num_clients": 1, "training_metrics": {"avg_loss": 0.012846229561546352, "avg_accuracy": 0.7665600180283643, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8848615081753815, "replay_buffer_size": 88}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 38, "timestamp": 1756275708.2702162, "num_clients": 1, "training_metrics": {"avg_loss": 0.02185906411371737, "avg_accuracy": 0.7747690258173753, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8812563743545131, "replay_buffer_size": 89}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 39, "timestamp": 1756275708.986964, "num_clients": 1, "training_metrics": {"avg_loss": 0.011883949263998753, "avg_accuracy": 0.7715738911496443, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8852464202944005, "replay_buffer_size": 90}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 40, "timestamp": 1756275709.6976979, "num_clients": 1, "training_metrics": {"avg_loss": 0.013679744077004822, "avg_accuracy": 0.7740399394011775, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8845281023691981, "replay_buffer_size": 91}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 41, "timestamp": 1756275710.4160092, "num_clients": 1, "training_metrics": {"avg_loss": 0.011920275312756226, "avg_accuracy": 0.7716479129589956, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8852318898748975, "replay_buffer_size": 92}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 42, "timestamp": 1756275711.1598632, "num_clients": 1, "training_metrics": {"avg_loss": 0.01025399812078831, "avg_accuracy": 0.7733515453238002, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8858984007516847, "replay_buffer_size": 93}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 43, "timestamp": 1756275711.866798, "num_clients": 1, "training_metrics": {"avg_loss": 0.010074867071277064, "avg_accuracy": 0.771214428679159, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8859700531714891, "replay_buffer_size": 94}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 44, "timestamp": 1756275712.5784712, "num_clients": 1, "training_metrics": {"avg_loss": 0.011128271186710966, "avg_accuracy": 0.7765506983127449, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8855486915253157, "replay_buffer_size": 95}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 45, "timestamp": 1756275713.3011177, "num_clients": 1, "training_metrics": {"avg_loss": 0.009440814252381339, "avg_accuracy": 0.7763490150672904, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8862236742990475, "replay_buffer_size": 96}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 46, "timestamp": 1756275714.0147152, "num_clients": 1, "training_metrics": {"avg_loss": 0.009792698805237402, "avg_accuracy": 0.7750924877413162, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.886082920477905, "replay_buffer_size": 97}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 47, "timestamp": 1756275714.7303588, "num_clients": 1, "training_metrics": {"avg_loss": 0.010224565700203433, "avg_accuracy": 0.7780957357062696, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8859101737199186, "replay_buffer_size": 98}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 48, "timestamp": 1756275715.465627, "num_clients": 1, "training_metrics": {"avg_loss": 0.008943232163649858, "avg_accuracy": 0.7744731902897732, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8864227071345401, "replay_buffer_size": 99}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}, {"round": 49, "timestamp": 1756275716.2035005, "num_clients": 1, "training_metrics": {"avg_loss": 0.009946708127851403, "avg_accuracy": 0.7734612831093055, "avg_delay": 0.0}, "network_metrics": {"avg_energy": NaN, "avg_round_time": NaN}, "maddpg_metrics": {"avg_reward": 0.8860213167488594, "replay_buffer_size": 100}, "task_similarity_metrics": {"shared_task_groups": 0, "independent_tasks": 3}}]}, "maddpg_stats": {"replay_buffer_size": 100, "num_agents": 5, "state_dim": 21, "action_dim": 12}, "success": true}