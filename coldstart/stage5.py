from typing import List
import numpy as np
from sklearn.cluster import SpectralClustering
from sklearn.metrics import silhouette_score
from sklearn.cluster import KMeans


# Step 2: 使用轮廓系数选择最优 k
def _select_optimal_k(S, max_k=5):
    """
    基于轮廓系数选择最优聚类数 k
    注意：silhouette_score 使用 euclidean 距离，这里把 S 当作“特征”使用
    更严谨的做法是先做嵌入（如谱嵌入），但我们简化处理
    """
    if max_k >= len(S):
        max_k = len(S) - 1

    k_range = range(2, max_k + 1)
    silhouette_scores = []

    for k in k_range:
        # 使用谱聚类，输入 precomputed 相似度
        clustering = SpectralClustering(
            n_clusters=k,
            affinity="precomputed",
            random_state=42,
            n_init=10,  # sklearn 1.3+ 需要指定
        )
        labels = clustering.fit_predict(S)

        # 计算轮廓系数（注意：不能直接用 precomputed + silhouette_score）
        # 所以我们换一种方式：将相似度矩阵转换为“距离表示”作为输入 X
        # 方法：用 1 - S 作为距离，但 silhouette_score 需要样本间距离矩阵或特征
        # 这里我们使用谱嵌入后的低维表示来计算轮廓系数（更合理）

        # 更准确的做法：先做谱嵌入，再 KMeans，再 silhouette_score
        from sklearn.preprocessing import normalize
        from scipy.linalg import eigh

        W = S
        D = np.diag(W.sum(axis=1))
        L = D - W
        # 归一化拉普拉斯
        D_inv_sqrt = np.diag(1.0 / (np.sqrt(np.diag(D)) + 1e-8))
        L_norm = D_inv_sqrt @ L @ D_inv_sqrt

        # 特征分解
        eigenvals, eigenvecs = eigh(L_norm)
        X_embedded = eigenvecs[:, :k]  # 取前 k 维

        # 在嵌入空间聚类
        labels_embedded = KMeans(n_clusters=k, random_state=42).fit_predict(X_embedded)
        score = silhouette_score(X_embedded, labels_embedded, metric="euclidean")
        silhouette_scores.append(score)
        print(f"k={k}, 轮廓系数 = {score:.3f}")

    # 选择轮廓系数最高的 k
    best_k = k_range[np.argmax(silhouette_scores)]
    return best_k, silhouette_scores, k_range


def task_grouping(S, max_k=3) -> List[List[int]]:

    # 执行 k 选择
    best_k, scores, k_range = _select_optimal_k(S, max_k=4)

    print(f"最优聚类数 k = {best_k}")

    # Step 3: 使用最优 k 进行谱聚类
    final_clustering = SpectralClustering(
        n_clusters=best_k, affinity="precomputed", random_state=42
    )
    labels = final_clustering.fit_predict(S)

    # 输出结果
    print("任务分组结果：")
    res = {}
    for i, label in enumerate(labels):
        if label not in res:
            res[label] = []
        res[label].append(i)
        print(f"任务 {i} → 簇 {label}")
    return res.values()


if __name__ == "__main__":
    # import matplotlib.pyplot as plt
    # Step 1: 输入任务相似度矩阵 S (N x N)
    # 示例：4 个任务，两两之间的相似度
    S = np.array(
        [
            [1.0, 0.9, 0.1, 0.2],
            [0.9, 1.0, 0.1, 0.1],
            [0.1, 0.1, 1.0, 0.8],
            [0.2, 0.1, 0.8, 1.0],
        ]
    )
    task_grouping(S, max_k=4)
