import numpy as np
import pandas as pd

from coldstart.stage1_img import extract_img_data_set_name_features
from coldstart.stage1_text import extract_text_data_set_name_features
from coldstart.stage4 import compute_task_similarity_from_virtual_coords
from coldstart.stage3 import run_virtual_coordinate_optimization
from coldstart.stage2 import grouping_and_pruning_process


def compute_task_similarity_from_data_set(data_sets_names_dict=None, gamma=0.07):
    # step 1 数据集特征提取
    datasets = []
    datasets_names = []
    for type_name, data_sets_names in data_sets_names_dict.items():
        if "img" in type_name:
            for data_name in data_sets_names:
                print(f"正在处理Img数据集: {data_name}")
                features = extract_img_data_set_name_features(data_name=data_name)
                datasets.append(features)
                datasets_names.append(data_name)
        elif "audio" in type_name:
            # TODO 待接入音频数据
            pass
        elif "text" in type_name:
            for data_name in data_sets_names:
                print(f"正在处理Text数据集: {data_name}")
                features = extract_text_data_set_name_features(
                    data_name=data_name, retrain=False
                )
                datasets.append(features)
                datasets_names.append(data_name)
        else:
            raise ValueError(f"Unsupported data type: {type_name}")

    all_task_virtual_coords = []
    for idx in range(len(datasets)):
        # step 2 特征收集与子区域划分的最终输出
        grouped_subcentroids, _ = grouping_and_pruning_process(
            datasets[idx], K=10, L=5, alpha_range=(0, 1)
        )
        print(f"任务 {idx} 的分组子质心形状: {grouped_subcentroids.shape}")
        # 假设 grouped_subcentroids 的形状为 (10, 50, 128)
        grouped_subcentroids_flat = grouped_subcentroids.reshape(
            -1, grouped_subcentroids.shape[-1]
        )  # 展平为 (500, 128)
        print(f"任务 {idx} 的展平子质心形状: {grouped_subcentroids_flat.shape}")
        # step 3 虚拟坐标映射
        virtual_coords, _ = run_virtual_coordinate_optimization(
            grouped_subcentroids_flat
        )
        print(f"任务 {idx} 的虚拟坐标形状: {virtual_coords.shape}")
        all_task_virtual_coords.append(virtual_coords)

    # step 4 计算任务间相似度（相似度矩阵（基于高斯核），欧几里得距离）
    # 欧几里得距离数值越大 表示任务之间的差异越大，任务之间的相似度越低。
    # 欧几里得距离数值越小 表示任务之间的差异越小，任务之间的相似度越高。
    similarity_matrix, distance_matrix = compute_task_similarity_from_virtual_coords(
        all_task_virtual_coords, gamma=gamma
    )
    distance_matrix = pd.DataFrame(
        distance_matrix,
        columns=[datasets_names[i] for i in range(len(all_task_virtual_coords))],
        index=[datasets_names[i] for i in range(len(all_task_virtual_coords))],
    )
    print("任务间欧几里得距离：")
    print(distance_matrix)

    similarity_df = pd.DataFrame(
        similarity_matrix,
        columns=[datasets_names[i] for i in range(len(all_task_virtual_coords))],
        index=[datasets_names[i] for i in range(len(all_task_virtual_coords))],
    )
    print("任务间相似度矩阵：")
    print(similarity_df)
    return similarity_matrix, distance_matrix


def are_arrays_equal(arr1, arr2):
    """
    判断两个 numpy 数组是否相同
    """
    if np.array_equal(arr1, arr2):
        return True
    else:
        return False


if __name__ == "__main__":
    data_set_names_dict = {
        # 'audio': ['speech_commands'],
        "text": ["airquality"],
        "img": ["mnist", "cifar10"],
        # 'img': ['cifar10', 'cifar10']
    }
    similarity_matrix, distance_matrix = compute_task_similarity_from_data_set(
        data_sets_names_dict=data_set_names_dict, gamma=0.04
    )
    print(similarity_matrix)
    print("done")
