"""
Multi-Task Federated Learning Client

This module extends the existing federated learning client to handle multiple tasks
with task similarity analysis and resource allocation optimization.
"""

import logging
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import time
import random
import os
import pickle
import hashlib
from typing import Dict, List, Tuple, Optional, Any

# Import existing FL client
from entity.entitys import NetworkMetrics, ResourceAllocation, TaskConfig, TaskType
from federated_learning.client import Client

# Import existing similarity analysis
from coldstart.coldstart_stage import compute_task_similarity_from_data_set


class MultiTaskClient(Client):
    """
    Multi-task federated learning client that extends the existing Client class
    """

    def __init__(
        self, device: Any, client_id: int, task_configs: Dict[str, TaskConfig] = None
    ):
        # Initialize base client
        super().__init__(client_id)

        # Multi-task specific attributes
        self.task_configs = task_configs or self._get_default_tasks()
        self.logger = logging.getLogger(f"MTClient-{client_id}")

        # Task management
        self.task_models: Dict[str, nn.Module] = {}
        self.task_optimizers: Dict[str, optim.Optimizer] = {}
        self.task_data: Dict[str, Any] = {}

        # Task similarity and sharing
        self.similarity_matrix: Optional[np.ndarray] = None
        self.distance_matrix: Optional[np.ndarray] = None
        self.similarity_threshold: float = 0.5
        self.shared_tasks: List[List[str]] = []  # Groups of similar tasks
        self.independent_tasks: List[str] = []
        self.similarity_computed: bool = (
            False  # Flag to track if similarity has been computed
        )

        # Similarity cache settings
        self.similarity_cache_dir = "cache/similarity"
        self.similarity_cache_file = None

        # Resource allocation
        self.current_allocation: Optional[ResourceAllocation] = None
        self.network_metrics: List[NetworkMetrics] = None

        # Training state
        self.training_metrics: Dict[str, Dict[str, float]] = {}

        # Data storage for all tasks
        self.task_data_loaders: Dict[str, Tuple] = (
            {}
        )  # {task_name: (train_loader, test_loader)}

        self.device = device
        # Load data for all tasks first
        self.load_data()

        # Initialize tasks
        self._initialize_tasks()

        # Generate cache file name based on task configuration
        self._generate_similarity_cache_filename(client_id)

    def load_data(self):
        """在客户端初始化时加载所有任务的数据"""
        self.logger.info(f"Client {self.client_id}: Loading data for all tasks...")

        for task_name, task_config in self.task_configs.items():
            try:
                self.logger.info(f"Loading data for task: {task_name}")

                # 加载任务数据
                train_loader, test_loader = self._load_task_data(task_name, task_config)

                if train_loader is not None and test_loader is not None:
                    self.task_data_loaders[task_name] = (train_loader, test_loader)
                    train_samples = len(train_loader.dataset)
                    test_samples = len(test_loader.dataset)
                    self.logger.info(
                        f"Task '{task_name}': {train_samples} train, {test_samples} test samples"
                    )
                else:
                    self.logger.warning(
                        f"Failed to load data for task '{task_name}', will use simulation"
                    )
                    self.task_data_loaders[task_name] = (None, None)

            except Exception as e:
                self.logger.error(f"Error loading data for task '{task_name}': {e}")
                self.task_data_loaders[task_name] = (None, None)

        successful_loads = len(
            [
                name
                for name, (train, test) in self.task_data_loaders.items()
                if train is not None
            ]
        )
        total_tasks = len(self.task_configs)

        self.logger.info(
            f"Client {self.client_id}: Data loading completed - {successful_loads}/{total_tasks} tasks loaded successfully"
        )

        if successful_loads == 0:
            self.logger.warning(
                f"Client {self.client_id}: No real data loaded, will use simulation for all tasks"
            )

    def _get_default_tasks(self) -> Dict[str, TaskConfig]:
        """Get default task configurations"""
        return {
            "mnist": TaskConfig(
                name="mnist",
                task_type=TaskType.IMAGE_CLASSIFICATION,
                data_path="data/MNIST",
                model_type="cnn",
                batch_size=64,
                learning_rate=0.001,
                local_epochs=5,
                priority=1.0,
            ),
            "cifar10": TaskConfig(
                name="cifar10",
                task_type=TaskType.IMAGE_CLASSIFICATION,
                data_path="data/cifar-10-batches-py",
                model_type="resnet",
                batch_size=32,
                learning_rate=0.0001,
                local_epochs=5,
                priority=1.0,
            ),
            "airquality": TaskConfig(
                name="airquality",
                task_type=TaskType.TIME_SERIES,
                data_path="data/air_quality",
                model_type="gru",
                batch_size=32,
                learning_rate=0.001,
                local_epochs=5,
                priority=0.8,
            ),
        }

    def _generate_similarity_cache_filename(self, client_id: int):
        """Generate cache filename based on task configuration"""
        # Create a hash of task configurations to ensure uniqueness
        task_info = []
        for task_name, config in sorted(self.task_configs.items()):
            task_info.append(
                f"{task_name}_{config.task_type.value}_{config.model_type}"
            )

        task_string = "_".join(task_info)
        task_hash = hashlib.md5(task_string.encode()).hexdigest()[:8]

        # Create cache directory if it doesn't exist
        os.makedirs(self.similarity_cache_dir, exist_ok=True)

        self.similarity_cache_file = os.path.join(
            self.similarity_cache_dir, f"similarity_{client_id}_{task_hash}.pkl"
        )

        self.logger.info(f"Similarity cache file: {self.similarity_cache_file}")

    def _load_similarity_from_cache(self) -> bool:
        """Load similarity matrix from cache file if it exists"""
        if not self.similarity_cache_file or not os.path.exists(
            self.similarity_cache_file
        ):
            self.logger.info("No similarity cache file found")
            return False

        try:
            with open(self.similarity_cache_file, "rb") as f:
                cache_data = pickle.load(f)

            self.similarity_matrix = cache_data["similarity_matrix"]
            self.distance_matrix = cache_data["distance_matrix"]
            self.similarity_computed = True

            # Determine task grouping based on loaded similarity
            self._determine_task_grouping()

            self.logger.info(
                f"Loaded similarity matrix from cache: {self.similarity_cache_file}"
            )
            self.logger.info(f"Shared task groups: {self.shared_tasks}")
            self.logger.info(f"Independent tasks: {self.independent_tasks}")

            return True

        except Exception as e:
            self.logger.error(f"Failed to load similarity cache: {e}")
            return False

    def _save_similarity_to_cache(self):
        """Save similarity matrix to cache file"""
        if not self.similarity_cache_file:
            return

        try:
            cache_data = {
                "similarity_matrix": self.similarity_matrix,
                "distance_matrix": self.distance_matrix,
                "task_configs": {
                    name: {
                        "name": config.name,
                        "task_type": config.task_type.value,
                        "model_type": config.model_type,
                    }
                    for name, config in self.task_configs.items()
                },
                "shared_tasks": self.shared_tasks,
                "independent_tasks": self.independent_tasks,
                "similarity_threshold": self.similarity_threshold,
            }

            with open(self.similarity_cache_file, "wb") as f:
                pickle.dump(cache_data, f)

            self.logger.info(
                f"Saved similarity matrix to cache: {self.similarity_cache_file}"
            )

        except Exception as e:
            self.logger.error(f"Failed to save similarity cache: {e}")

    def _initialize_tasks(self):
        """Initialize all tasks"""
        self.logger.info(f"Initializing {len(self.task_configs)} tasks")

        for task_name, config in self.task_configs.items():
            try:
                # Initialize model for each task
                self._initialize_task_model(task_name, config)
                self.logger.info(f"Initialized task: {task_name}")

            except Exception as e:
                self.logger.error(f"Failed to initialize task {task_name}: {e}")

    def _initialize_task_model(self, task_name: str, config: TaskConfig):
        """Initialize model for a specific task"""
        if config.model_type == "cnn":
            model = self._create_cnn_model(task_name)  # 传递任务名称以选择合适的架构
        elif config.model_type == "resnet":
            model = self._create_resnet_model()
        elif config.model_type == "gru":
            model = self._create_gru_model()
        else:
            raise ValueError(f"Unsupported model type: {config.model_type}")

        self.task_models[task_name] = model

        # Initialize optimizer
        optimizer = optim.Adam(model.parameters(), lr=config.learning_rate)
        self.task_optimizers[task_name] = optimizer

    def _create_cnn_model(self, task_name: str = "") -> nn.Module:
        """Create CNN model for image classification (adaptive for different datasets)"""

        if "mnist" in task_name.lower():
            # MNIST: 1-channel, 28x28
            return nn.Sequential(
                nn.Conv2d(1, 32, 3, padding=1),  # 1 channel for grayscale MNIST
                nn.ReLU(),
                nn.MaxPool2d(2),  # 28x28 -> 14x14
                nn.Conv2d(32, 64, 3, padding=1),
                nn.ReLU(),
                nn.MaxPool2d(2),  # 14x14 -> 7x7
                nn.Flatten(),
                nn.Linear(64 * 7 * 7, 128),
                nn.ReLU(),
                nn.Dropout(0.5),
                nn.Linear(128, 10),
            )
        elif "cifar" in task_name.lower():
            # CIFAR-10: 3-channel, 32x32
            return nn.Sequential(
                nn.Conv2d(3, 32, 3, padding=1),  # 3 channels for RGB CIFAR-10
                nn.ReLU(),
                nn.MaxPool2d(2),  # 32x32 -> 16x16
                nn.Conv2d(32, 64, 3, padding=1),
                nn.ReLU(),
                nn.MaxPool2d(2),  # 16x16 -> 8x8
                nn.Conv2d(64, 128, 3, padding=1),
                nn.ReLU(),
                nn.MaxPool2d(2),  # 8x8 -> 4x4
                nn.Flatten(),
                nn.Linear(128 * 4 * 4, 256),
                nn.ReLU(),
                nn.Dropout(0.5),
                nn.Linear(256, 10),
            )
        else:
            # 默认：3-channel, 适用于一般图像
            return nn.Sequential(
                nn.Conv2d(3, 32, 3, padding=1),
                nn.ReLU(),
                nn.MaxPool2d(2),
                nn.Conv2d(32, 64, 3, padding=1),
                nn.ReLU(),
                nn.MaxPool2d(2),
                nn.Flatten(),
                nn.Linear(64 * 8 * 8, 128),  # 假设32x32输入
                nn.ReLU(),
                nn.Dropout(0.5),
                nn.Linear(128, 10),
            )

    def _create_resnet_model(self) -> nn.Module:
        """Create ResNet model for image classification"""
        try:
            import torchvision.models as models

            model = models.resnet18(pretrained=False)
            model.fc = nn.Linear(model.fc.in_features, 10)
            return model
        except ImportError:
            # Fallback to simple CNN if torchvision not available
            return self._create_cnn_model()

    def _create_gru_model(self) -> nn.Module:
        """Create GRU model for time series prediction"""

        class GRUModel(nn.Module):
            def __init__(self, input_size=1):
                super(GRUModel, self).__init__()
                # Air Quality数据有多个特征，根据data_utils.py，应该是1个特征（Previous CO(GT)）
                self.gru = nn.GRU(
                    input_size=input_size, hidden_size=64, batch_first=True
                )
                self.fc = nn.Sequential(nn.Linear(64, 32), nn.ReLU(), nn.Linear(32, 1))

            def forward(self, x):
                # x shape: (batch_size, seq_len, features)
                output, _ = self.gru(x)
                # 取最后一个时间步的输出
                return self.fc(output[:, -1, :])

        # Air Quality数据根据data_utils.py有1个特征（Previous CO(GT)）
        return GRUModel(input_size=1)

    def analyze_task_similarity(
        self, gamma: float = 0.04, force_recompute: bool = False
    ):
        """Analyze task similarity using existing coldstart method (persistent cache)"""
        # First, try to load from persistent cache
        if not force_recompute and self._load_similarity_from_cache():
            self.logger.info(
                f"Client {self.client_id}: Using persistent cached similarity matrix"
            )
            return self.similarity_matrix, self.distance_matrix

        # Check if similarity has already been computed in this session and we don't want to force recompute
        if self.similarity_computed and not force_recompute:
            self.logger.info(
                f"Client {self.client_id}: Using session cached similarity matrix"
            )
            self.logger.info(f"  Shared task groups: {self.shared_tasks}")
            self.logger.info(f"  Independent tasks: {self.independent_tasks}")
            return self.similarity_matrix, self.distance_matrix

        self.logger.info(
            f"Client {self.client_id}: Computing task similarity matrix..."
        )

        # Prepare data for similarity analysis
        data_sets_dict = {"img": [], "text": []}

        for task_name, config in self.task_configs.items():
            if config.task_type == TaskType.IMAGE_CLASSIFICATION:
                data_sets_dict["img"].append(task_name)
            elif config.task_type == TaskType.TIME_SERIES:
                data_sets_dict["text"].append(task_name)

        # Remove empty categories
        data_sets_dict = {k: v for k, v in data_sets_dict.items() if v}

        try:
            # Compute similarity using existing method
            similarity_matrix, distance_matrix = compute_task_similarity_from_data_set(
                data_sets_names_dict=data_sets_dict, gamma=gamma
            )

            self.similarity_matrix = similarity_matrix
            self.distance_matrix = distance_matrix
            self.similarity_computed = True  # Mark as computed

            # Determine task grouping based on similarity
            self._determine_task_grouping()

            # Save to persistent cache
            self._save_similarity_to_cache()

            self.logger.info(
                f"Client {self.client_id}: Task similarity analysis completed and cached"
            )
            self.logger.info(f"  Shared task groups: {self.shared_tasks}")
            self.logger.info(f"  Independent tasks: {self.independent_tasks}")

            return similarity_matrix, distance_matrix

        except Exception as e:
            self.logger.error(
                f"Client {self.client_id}: Task similarity analysis failed: {e}"
            )
            # Fallback: treat all tasks as independent
            self.independent_tasks = list(self.task_configs.keys())
            self.shared_tasks = []
            self.similarity_computed = (
                True  # Mark as computed even if failed to avoid retrying
            )
            return None, None

    def _determine_task_grouping(self):
        """Determine task grouping based on similarity matrix"""
        if self.similarity_matrix is None:
            self.independent_tasks = list(self.task_configs.keys())
            self.shared_tasks = []
            return

        task_names = list(self.task_configs.keys())
        n_tasks = len(task_names)
        visited = [False] * n_tasks

        self.shared_tasks = []
        self.independent_tasks = []

        for i in range(n_tasks):
            if visited[i]:
                continue

            # Find similar tasks
            similar_group = [task_names[i]]
            visited[i] = True

            for j in range(i + 1, n_tasks):
                if (
                    not visited[j]
                    and self.similarity_matrix[i, j] > self.similarity_threshold
                ):
                    similar_group.append(task_names[j])
                    visited[j] = True

            # Group tasks
            if len(similar_group) > 1:
                self.shared_tasks.append(similar_group)
            else:
                self.independent_tasks.extend(similar_group)

    def set_resource_allocation(self, allocation: ResourceAllocation):
        """Set resource allocation from MADDPG"""
        self.current_allocation = allocation

    def set_network_metrics(self, metrics: List[NetworkMetrics]):
        """Set network metrics from NS3 simulation"""
        self.network_metrics = metrics
        for metric in metrics:
            self.logger.info(
                f"Network metrics updated: Delay={metric.delay:.3f}s, Energy={metric.energy_use:.3f}J"
            )

    def get_state_for_maddpg(self) -> Dict[str, Any]:
        """Get current state for MADDPG agent in dictionary format"""
        # [select_ij, power_ij, task_ij, q_ij, energy_use_ij, delay_ij, accuracy_ij]
        # self.state_dim = 7 * num_clients * num_tasks  # = 7 for 3 tasks
        state = []
        # TODO get_state_for_maddpg
        for i, task_name in enumerate(self.task_configs.keys()):
            key = f"task_{i}"
            # state.append(self.current_allocation.B_ij[key])
            state.append(self.current_allocation.select_ij[key])
            state.append(self.current_allocation.P_ij[key])
            state.append(self.current_allocation.Task_ij[key])
            state.append(self.current_allocation.q_ij[key])

            has_simu = False
            for metric in self.network_metrics:
                if metric.task_name in key:
                    state.append(metric.energy_use)
                    state.append(metric.delay)
                    has_simu = True
            if not has_simu:
                state.append(0.0)
                state.append(0.0)

            state.append(self.training_metrics[task_name]["accuracy"])

        return state

    def run(self):
        """Run client training (override base class method)"""
        start_time = time.time()

        # Train each task based on grouping strategy
        round_metrics = {}

        # Train shared task groups
        for group in self.shared_tasks:
            group_metrics = self._train_shared_tasks(group)
            round_metrics.update(group_metrics)

        # Train independent tasks
        for task_name in self.independent_tasks:
            task_metrics = self._train_single_task(task_name)
            round_metrics[task_name] = task_metrics

        # Update training metrics
        self.training_metrics.update(round_metrics)

        training_time = time.time() - start_time
        self.logger.info(f"Multi-task training completed in {training_time:.2f}s")

        return round_metrics

    def receive_global_model(self, global_weights: Dict[str, Any]):
        """接收服务器发送的全局模型权重"""
        try:
            self.logger.info(f"Client {self.client_id}: Receiving global model weights")

            # 更新本地模型权重
            for task_name, model in self.task_models.items():
                if task_name in global_weights:
                    # 加载全局模型权重到本地模型
                    model.load_state_dict(global_weights[task_name])
                    self.logger.debug(f"Updated {task_name} model with global weights")

            self.logger.info(
                f"Client {self.client_id}: Global model weights applied to {len(global_weights)} tasks"
            )

        except Exception as e:
            self.logger.error(
                f"Client {self.client_id}: Failed to receive global model: {e}"
            )

    def get_model_weights(self) -> Dict[str, Any]:
        """获取当前本地模型权重用于上传到服务器"""
        try:
            weights = {}
            for task_name, model in self.task_models.items():
                weights[task_name] = model.state_dict()

            self.logger.debug(
                f"Client {self.client_id}: Extracted weights for {len(weights)} tasks"
            )
            return weights

        except Exception as e:
            self.logger.error(
                f"Client {self.client_id}: Failed to extract model weights: {e}"
            )
            return {}

    def _train_shared_tasks(self, task_group: List[str]) -> Dict[str, Dict[str, float]]:
        """Train a group of similar tasks with shared model"""
        self.logger.info(f"Training shared task group: {task_group}")

        # For simplicity, use the first task's model as shared model
        primary_task = task_group[0]
        shared_model = self.task_models[primary_task]
        shared_optimizer = self.task_optimizers[primary_task]

        metrics = {}

        for task_name in task_group:
            # Get resource allocation for this task
            compute_alloc = (
                self.current_allocation.q_ij.get(task_name, 0.2)
                if self.current_allocation
                else 0.2
            )

            # Train with allocated resources (simulate by limiting epochs)
            allocated_epochs = max(
                1, int(self.task_configs[task_name].local_epochs * compute_alloc)
            )

            task_metrics = self._simulate_task_training(task_name, allocated_epochs)
            metrics[task_name] = task_metrics

        return metrics

    def _train_single_task(self, task_name: str) -> Dict[str, float]:
        """Train a single independent task"""
        self.logger.info(f"Training independent task: {task_name}")

        # Get resource allocation
        compute_alloc = (
            self.current_allocation.q_ij.get(task_name, 0.3)
            if self.current_allocation
            else 0.3
        )
        allocated_epochs = max(
            1, int(self.task_configs[task_name].local_epochs * compute_alloc)
        )

        return self._simulate_task_training(task_name, allocated_epochs)

    def _simulate_task_training(self, task_name: str, epochs: int) -> Dict[str, float]:
        """真正的任务训练 - 使用预加载的数据"""
        self.logger.info(
            f"Starting training for task '{task_name}' with {epochs} epochs"
        )

        try:
            # 获取任务配置和模型
            task_config = self.task_configs[task_name]
            model = self.task_models[task_name]
            optimizer = self.task_optimizers[task_name]

            # 使用预加载的数据
            if task_name in self.task_data_loaders:
                train_loader, test_loader = self.task_data_loaders[task_name]

                if train_loader is not None and test_loader is not None:
                    self.logger.info(
                        f"Using pre-loaded real data for task '{task_name}'"
                    )

                    # 执行真正的训练
                    training_metrics = self._execute_real_training(
                        model, optimizer, train_loader, test_loader, epochs, task_name
                    )

                    self.logger.info(
                        f"Task '{task_name}' real training completed: "
                        f"loss={training_metrics['loss']:.4f}, "
                        f"accuracy={training_metrics['accuracy']:.4f}"
                    )

                    return training_metrics
                else:
                    self.logger.warning(
                        f"No real data available for task {task_name}, using simulation"
                    )
                    return self._fallback_simulation(task_name, epochs)
            else:
                self.logger.warning(
                    f"Task {task_name} not found in data loaders, using simulation"
                )
                return self._fallback_simulation(task_name, epochs)

        except Exception as e:
            self.logger.error(f"Training failed for task {task_name}: {e}")
            # 回退到模拟训练
            return self._fallback_simulation(task_name, epochs)

    def _load_task_data(self, task_name: str, task_config):
        """使用data_utils中的方法加载任务的真实数据"""
        try:
            # 导入data_utils中的数据加载函数
            from coldstart.data_utils import load_mnist, load_cifar10, load_air_quality

            if task_config.task_type == TaskType.IMAGE_CLASSIFICATION:
                return self._load_image_data_with_utils(
                    task_name, task_config, load_mnist, load_cifar10
                )
            elif task_config.task_type == TaskType.TIME_SERIES:
                return self._load_time_series_data_with_utils(
                    task_name, task_config, load_air_quality
                )
            else:
                self.logger.warning(f"Unknown task type: {task_config.task_type}")
                return None, None

        except Exception as e:
            self.logger.error(f"Failed to load data for task {task_name}: {e}")
            return None, None

    def _load_image_data_with_utils(
        self, task_name: str, task_config, load_mnist, load_cifar10
    ):
        """使用data_utils加载图像分类数据，为CNN模型提供合适的通道数"""
        from torch.utils.data import DataLoader, Subset

        try:
            if "mnist" in task_name.lower():
                # 为CNN模型加载1通道MNIST数据（不使用data_utils的3通道转换）
                train_dataset, test_dataset = self._load_mnist_for_cnn(
                    task_config.data_path
                )
                self.logger.info(
                    f"Loaded MNIST for CNN (1-channel): {len(train_dataset)} train, {len(test_dataset)} test"
                )

            elif "cifar" in task_name.lower():
                # CIFAR-10本身就是3通道，可以使用data_utils，但需要调整模型
                train_dataset, test_dataset = self._load_cifar10_for_cnn(
                    task_config.data_path
                )
                self.logger.info(
                    f"Loaded CIFAR-10 for CNN (3-channel): {len(train_dataset)} train, {len(test_dataset)} test"
                )

            else:
                self.logger.warning(f"Unknown image dataset: {task_name}")
                return None, None

            # 为联邦学习创建客户端数据子集
            client_data_size = min(
                1000, len(train_dataset) // 10
            )  # 每个客户端最多1000个样本
            start_idx = (self.client_id * client_data_size) % len(train_dataset)
            end_idx = min(start_idx + client_data_size, len(train_dataset))

            client_indices = list(range(start_idx, end_idx))
            client_train_dataset = Subset(train_dataset, client_indices)

            # 测试集也取一个子集
            test_indices = list(range(0, min(200, len(test_dataset))))
            client_test_dataset = Subset(test_dataset, test_indices)

            # 创建数据加载器
            batch_size = max(16, min(64, len(client_train_dataset) // 4))

            train_loader = DataLoader(
                client_train_dataset, batch_size=batch_size, shuffle=True
            )
            test_loader = DataLoader(
                client_test_dataset, batch_size=batch_size, shuffle=False
            )

            self.logger.info(
                f"Created data loaders for {task_name}: {len(client_train_dataset)} train, {len(client_test_dataset)} test samples"
            )

            return train_loader, test_loader

        except Exception as e:
            self.logger.error(
                f"Failed to load image data for {task_name} using data_utils: {e}"
            )
            return None, None

    def _load_mnist_for_cnn(self, data_path: str):
        """专门为CNN模型加载1通道MNIST数据"""
        import torchvision
        import torchvision.transforms as transforms

        try:
            # 为CNN模型设计的MNIST预处理：保持1通道，28x28尺寸
            transform = transforms.Compose(
                [
                    transforms.ToTensor(),
                    transforms.Normalize((0.1307,), (0.3081,)),  # MNIST标准化参数
                ]
            )

            train_dataset = torchvision.datasets.MNIST(
                root=data_path, train=True, download=False, transform=transform
            )
            test_dataset = torchvision.datasets.MNIST(
                root=data_path, train=False, download=False, transform=transform
            )

            self.logger.info(
                f"Loaded MNIST for CNN: 1-channel, 28x28, {len(train_dataset)} train, {len(test_dataset)} test"
            )
            return train_dataset, test_dataset

        except Exception as e:
            self.logger.error(f"Failed to load MNIST for CNN: {e}")
            return None, None

    def _load_cifar10_for_cnn(self, data_path: str):
        """专门为CNN模型加载3通道CIFAR-10数据"""
        import torchvision
        import torchvision.transforms as transforms

        try:
            # 为CNN模型设计的CIFAR-10预处理：保持3通道，32x32尺寸
            transform = transforms.Compose(
                [
                    transforms.ToTensor(),
                    transforms.Normalize(
                        (0.5, 0.5, 0.5), (0.5, 0.5, 0.5)
                    ),  # CIFAR-10标准化参数
                ]
            )

            train_dataset = torchvision.datasets.CIFAR10(
                root=data_path, train=True, download=False, transform=transform
            )
            test_dataset = torchvision.datasets.CIFAR10(
                root=data_path, train=False, download=False, transform=transform
            )

            self.logger.info(
                f"Loaded CIFAR-10 for CNN: 3-channel, 32x32, {len(train_dataset)} train, {len(test_dataset)} test"
            )
            return train_dataset, test_dataset

        except Exception as e:
            self.logger.error(f"Failed to load CIFAR-10 for CNN: {e}")
            return None, None

    def _load_time_series_data_with_utils(
        self, task_name: str, task_config, load_air_quality
    ):
        """使用data_utils加载时间序列数据 (Air Quality)"""
        from torch.utils.data import DataLoader, Subset

        try:
            if "air" in task_name.lower() or "quality" in task_name.lower():
                # 使用data_utils中的load_air_quality函数
                train_dataset, test_dataset = load_air_quality(task_config.data_path)
                self.logger.info(
                    f"Loaded Air Quality using data_utils: {len(train_dataset)} train, {len(test_dataset)} test"
                )

                # 为联邦学习创建客户端数据子集
                client_data_size = min(
                    500, len(train_dataset) // 5
                )  # 每个客户端最多500个样本
                start_idx = (self.client_id * client_data_size) % len(train_dataset)
                end_idx = min(start_idx + client_data_size, len(train_dataset))

                client_indices = list(range(start_idx, end_idx))
                client_train_dataset = Subset(train_dataset, client_indices)

                # 测试集也取一个子集
                test_indices = list(range(0, min(100, len(test_dataset))))
                client_test_dataset = Subset(test_dataset, test_indices)

                # 创建数据加载器
                batch_size = max(8, min(32, len(client_train_dataset) // 4))
                train_loader = DataLoader(
                    client_train_dataset, batch_size=batch_size, shuffle=True
                )
                test_loader = DataLoader(
                    client_test_dataset, batch_size=batch_size, shuffle=False
                )

                self.logger.info(
                    f"Created data loaders for {task_name}: {len(client_train_dataset)} train, {len(client_test_dataset)} test samples"
                )

                return train_loader, test_loader
            else:
                self.logger.warning(f"Unknown time series dataset: {task_name}")
                return None, None

        except Exception as e:
            self.logger.error(
                f"Failed to load time series data for {task_name} using data_utils: {e}"
            )
            return None, None

    def _execute_real_training(
        self, model, optimizer, train_loader, test_loader, epochs, task_name
    ):
        """执行真正的模型训练"""
        import torch
        import torch.nn as nn

        # device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = model.to(self.device)

        # 选择损失函数
        task_config = self.task_configs[task_name]
        if task_config.task_type == TaskType.IMAGE_CLASSIFICATION:
            criterion = nn.CrossEntropyLoss()
        elif task_config.task_type == TaskType.TIME_SERIES:
            criterion = nn.MSELoss()
        else:
            criterion = nn.MSELoss()  # 默认

        # 训练循环
        model.train()
        total_loss = 0.0
        num_batches = 0

        for epoch in range(epochs):
            epoch_loss = 0.0
            epoch_batches = 0

            for batch_idx, (data, target) in enumerate(train_loader):
                data, target = data.to(self.device), target.to(self.device)

                # 处理时间序列数据的维度
                if task_config.task_type == TaskType.TIME_SERIES:
                    # Air Quality数据格式: (batch_size, features) -> (batch_size, 1, features)
                    if data.dim() == 2:
                        data = data.unsqueeze(1)  # 添加序列长度维度

                    # 确保目标是正确的形状
                    if target.dim() > 1:
                        target = target.squeeze()

                optimizer.zero_grad()
                output = model(data)

                # 确保输出和目标的形状匹配
                if task_config.task_type == TaskType.TIME_SERIES:
                    if output.dim() > 1 and output.size(-1) == 1:
                        output = output.squeeze(-1)  # 移除最后一个维度
                    if output.dim() > 1:
                        output = output.squeeze()  # 确保是1D

                loss = criterion(output, target)
                loss.backward()
                optimizer.step()

                epoch_loss += loss.item()
                epoch_batches += 1

            if epoch_batches > 0:
                avg_epoch_loss = epoch_loss / epoch_batches
                total_loss += avg_epoch_loss
                num_batches += 1

                self.logger.debug(
                    f"Task {task_name} Epoch {epoch+1}/{epochs}: Loss = {avg_epoch_loss:.4f}"
                )

        # 计算最终训练损失
        final_loss = total_loss / max(1, num_batches)

        # 评估模型
        accuracy = self._evaluate_model(model, test_loader, task_config, self.device)

        return {
            "loss": final_loss,
            "accuracy": accuracy,
            "epochs": epochs,
            "samples_trained": len(train_loader.dataset),
        }

    def _evaluate_model(self, model, test_loader, task_config, device):
        """评估模型性能"""
        import torch
        import torch.nn as nn

        model.eval()
        correct = 0
        total = 0
        test_loss = 0.0

        if task_config.task_type == TaskType.IMAGE_CLASSIFICATION:
            criterion = nn.CrossEntropyLoss()
        else:
            criterion = nn.MSELoss()

        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(device), target.to(device)

                # 处理时间序列数据的维度
                if task_config.task_type == TaskType.TIME_SERIES:
                    # Air Quality数据格式: (batch_size, features) -> (batch_size, 1, features)
                    if data.dim() == 2:
                        data = data.unsqueeze(1)  # 添加序列长度维度

                    # 确保目标是正确的形状
                    if target.dim() > 1:
                        target = target.squeeze()

                output = model(data)

                if task_config.task_type == TaskType.IMAGE_CLASSIFICATION:
                    # 分类任务：计算准确率
                    _, predicted = torch.max(output.data, 1)
                    total += target.size(0)
                    correct += (predicted == target).sum().item()
                else:
                    # 回归任务：计算R²分数或使用损失的倒数作为"准确率"
                    if output.dim() > 1 and output.size(-1) == 1:
                        output = output.squeeze(-1)  # 移除最后一个维度
                    if output.dim() > 1:
                        output = output.squeeze()  # 确保是1D

                    loss = criterion(output, target)
                    test_loss += loss.item()
                    total += 1

        if task_config.task_type == TaskType.IMAGE_CLASSIFICATION:
            accuracy = correct / total if total > 0 else 0.0
        else:
            # 对于回归任务，使用损失的倒数作为"准确率"指标
            avg_test_loss = test_loss / max(1, total)
            accuracy = 1.0 / (1.0 + avg_test_loss)  # 损失越小，"准确率"越高

        return accuracy

    def _fallback_simulation(self, task_name: str, epochs: int) -> Dict[str, float]:
        """回退到模拟训练（当真实数据不可用时）"""
        self.logger.info(f"Using fallback simulation for task {task_name}")

        # 基于资源分配模拟性能
        compute_alloc = (
            self.current_allocation.q_ij.get(task_name, 0.3)
            if self.current_allocation
            else 0.3
        )

        # 更好的资源分配 -> 更好的性能
        base_loss = 1.0
        loss_reduction = compute_alloc * 0.5 + np.random.normal(0, 0.1)
        final_loss = max(0.1, base_loss - loss_reduction)

        accuracy = min(0.95, 0.5 + compute_alloc * 0.4 + np.random.normal(0, 0.05))

        return {
            "loss": final_loss,
            "accuracy": accuracy,
            "epochs": epochs,
            "samples_trained": 0,  # 模拟训练没有真实样本
        }

    def get_report(self):
        """Get training report (override base class method)"""

        # Create a simple report object compatible with existing FL framework
        class Report:
            def __init__(self, client_id, weights, loss, delay):
                self.client_id = client_id
                self.weights = weights
                self.loss = loss
                self.delay = delay

        # Get weights from primary model (first task)
        if self.task_models:
            primary_task = list(self.task_models.keys())[0]
            weights = [
                (name, param.clone())
                for name, param in self.task_models[primary_task].named_parameters()
            ]
        else:
            weights = []

        # Calculate average loss
        avg_loss = (
            np.mean(
                [metrics.get("loss", 1.0) for metrics in self.training_metrics.values()]
            )
            if self.training_metrics
            else 1.0
        )

        # Use network delay if available
        delay = self.network_metrics.execution_time if self.network_metrics else 1.0

        return Report(self.client_id, weights, avg_loss, delay)

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        return {
            "client_id": self.client_id,
            "task_metrics": self.training_metrics,
            "shared_task_groups": self.shared_tasks,
            "independent_tasks": self.independent_tasks,
            "resource_allocation": {
                "select_ij": (
                    self.current_allocation.select_ij if self.current_allocation else {}
                ),
                "q_ij": self.current_allocation.q_ij if self.current_allocation else {},
                # "B_ij": self.current_allocation.B_ij if self.current_allocation else {},
                "P_ij": self.current_allocation.P_ij if self.current_allocation else {},
                "Task_ij": (
                    self.current_allocation.Task_ij if self.current_allocation else {}
                ),
            },
            "network_metrics": {
                "round_time": (
                    self.network_metrics.execution_time if self.network_metrics else 0.0
                ),
                "throughput": (
                    self.network_metrics.throughput if self.network_metrics else 0.0
                ),
            },
            "similarity_info": {
                "shared_groups": self.shared_tasks,
                "independent_tasks": self.independent_tasks,
                "similarity_threshold": self.similarity_threshold,
            },
        }
